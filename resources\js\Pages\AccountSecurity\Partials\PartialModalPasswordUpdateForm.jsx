//* PACKAGES
import React, { useState, useEffect } from 'react';
import { toast } from "react-toastify";
import { useForm } from 'laravel-precognition-react-inertia';

//* ICONS
import { AiFillEyeInvisible, AiOutlineEye } from "react-icons/ai";

//* COMPONENTS
import Modal from "@/Components/Modal";
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import DangerButton from '@/Components/DangerButton';
import AppPasswordStrengthIndicatorComponent from '@/Components/App/AppPasswordStrengthIndicatorComponent';;

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialModalPasswordUpdateForm(
    {
        //! VARIABLES
        //...

        //! STATES 
        isModalOpen,

        //! EVENTS
        handleModalClose,
    }
) {
    //! STATES 
    const [stateShowCurrentPassword, setStateShowCurrentPassword] = useState(false);
    const [stateShowNewPassword, setStateShowNewPassword] = useState(false);
    const [stateShowNewPasswordConfirm, setStateShowNewPasswordConfirm] = useState(false);

    //! VARIABLES 
    const form = useForm(
        'patch',
        route("user-settings.password.update"),
        {
            currentPassword: '',
            newPassword: '',
            newPassword_confirmation: '',
        }
    );

    const inputFields =
        [
            {
                label: 'Current Password',
                name: 'currentPassword',
                placeholder: 'Current Password',
                autoComplete: 'current-password',
                showFieldValue: stateShowCurrentPassword,
                value: form.data.currentPassword,
                errorMessage: form.errors.currentPassword,
                note: null,
                eventOnChange: (e => form.setData('currentPassword', e.target.value)),
                eventOnShow: (() => setStateShowCurrentPassword(!stateShowCurrentPassword))

            },
            {
                label: 'New Password',
                name: 'newPassword',
                placeholder: 'New Password',
                autoComplete: 'off',
                showFieldValue: stateShowNewPassword,
                value: form.data.newPassword,
                errorMessage: form.errors.newPassword,
                note: "Password must contain at least 8 characters, including 1 digit, 1 uppercase letter, 1 lowercase letter & 1 special character.",
                eventOnChange: (e => form.setData('newPassword', e.target.value)),
                eventOnShow: (() => setStateShowNewPassword(!stateShowNewPassword))
            },
            {
                label: 'Confirm New Password',
                name: 'confirmPassword',
                placeholder: 'Confirm New Password',
                autoComplete: 'off',
                showFieldValue: stateShowNewPasswordConfirm,
                value: form.data.newPassword_confirmation,
                errorMessage: form.errors.newPassword_confirmation,
                note: null,
                eventOnChange: (e => form.setData('newPassword_confirmation', e.target.value)),
                eventOnShow: (() => setStateShowNewPasswordConfirm(!stateShowNewPasswordConfirm))
            }
        ];

    //! FUNCTIONS 
    function handleSelfClose() {
        //* CLOSE MODAL
        form.reset();
        form.clearErrors();
        handleModalClose();
    }

    function handleSubmit(e) {
        e.preventDefault();

        form.clearErrors();

        form.submit(
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Password successfully changed.");
                    form.reset();

                    handleSelfClose();
                },
            }
        )
    }

    return (
        <Modal
            show={isModalOpen}
            closeable={false}
            onClose={handleSelfClose}
            maxWidth='2xl'
        >
            <form
                onSubmit={handleSubmit}
                className={`
                    flex flex-col justify-around
                    px-10 py-5
                    gap-y-5
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex gap-10 text-primary font-semibold text-lg'
                >
                    Password Update
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-4'
                >
                    {
                        inputFields.map(
                            (item, index) => {
                                return (
                                    <div
                                        key={index}
                                        className='flex flex-col gap-y-2'
                                    >
                                        <InputLabel
                                            forInput={item.name}
                                            value={item.label}
                                            className={'select-none'}
                                        />
                                        <div
                                            className='relative'
                                        >
                                            <TextInput
                                                value={item.value}
                                                type={item.showFieldValue == true ? "text" : "password"}
                                                name={item.name}
                                                placeholder={item.label}
                                                className="w-full"
                                                autoComplete={item.autoComplete}
                                                handleChange={e => item.eventOnChange(e)}
                                            />
                                            <div className="absolute top-2 right-5">
                                                <i
                                                    onClick={item.eventOnShow}
                                                    className="text-xl text-gray-400 cursor-pointer ease-in duration-200 hover:text-blue-500"
                                                >
                                                    {
                                                        item.showFieldValue
                                                            ?
                                                            <AiFillEyeInvisible
                                                                className='h-6 w-6'
                                                            />
                                                            :
                                                            <AiOutlineEye
                                                                className='h-6 w-6'
                                                            />
                                                    }
                                                </i>
                                            </div>
                                        </div>
                                        <InputError message={item.errorMessage} />

                                        {
                                            item.note == null || item.errorMessage != null
                                                ?
                                                null
                                                :
                                                (
                                                    <span className='text-xs text-gray-400 [&::first-letter]:uppercase'>{item.note}</span>
                                                )
                                        }

                                        {
                                            item.name == 'newPassword' && item.value.length > 0
                                                ?
                                                <AppPasswordStrengthIndicatorComponent
                                                    password={item.value}
                                                />
                                                :
                                                null
                                        }

                                    </div>
                                );
                            }
                        )
                    }
                </section>

                {/* SECTION FOOTER */}
                <section
                    className='flex justify-end gap-x-5'
                >
                    <DangerButton
                        type='button'
                        onClick={handleSelfClose}
                    >
                        Close
                    </DangerButton>
                    <PrimaryButton
                        type='submit'
                    >
                        Update
                    </PrimaryButton>
                </section>
            </form>
        </Modal>
    );
}
