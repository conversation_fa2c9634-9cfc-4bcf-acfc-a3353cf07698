//* PACKAGES 
import React, { useState, useEffect } from "react";
import { Head, usePage, router } from "@inertiajs/react";

//* ICONS
import { FaLock } from "react-icons/fa";
import { TbLockCheck } from "react-icons/tb";

//* COMPONENTS
import AppLogoWithTextComponent from "@/Components/App/AppLogoWithTextComponent";
import AppToggleSwitchComponent from "@/Components/App/AppToggleSwitchComponent";
import UserLayout from "@/Layouts/UserLayout";

//* PARTIALS
import PartialUserAccountSetupTwoFactorAuthToggleEmail from "./Partials/PartialUserAccountSetupTwoFactorAuthToggleEmail";
import PartialUserAccountSetupTwoFactorAuthToggleAuthApp from "./Partials/PartialUserAccountSetupTwoFactorAuthToggleAuthApp";

//* STATE
//...

//* UTILS 
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserAccountSetupTwoFactorAuth(
    {
        secretKey,
        qrCodeImageUrl
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! VARIABLES
    //...

    //! STATES
    const [stateToggleShowOptions, setStateToggleShowOptions] = useState(user.enable_email_otp == true || user.enable_authenticator_app == true);

    //! USE EFFECTS
    useEffect(
        () => {
            window.history.pushState(null, '', window.location.href);
            window.onpopstate = () => {
                window.history.pushState(null, '', window.location.href);
            };
        },
        []
    );

    //! FUNCTIONS   
    function handleFinishSetup() {
        router.patch(
            route('user-account-setup.two-factor.finish'),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
                onSuccess: () => {
                    //...
                },
                onError: () => toast.error("Something went wrong!")
            }
        );
    }

    return (
        <UserLayout
            postRouteName="user-account-setup.two-factor"
            setup={false}
            verified={true}
            additionalMainElementClassNames="bg-gray-100"
        >
            <Head
                title="2FA Setup"
            />
            <div
                className="flex flex-col sm:justify-center items-center mt-40"
            >
                <div
                    className="flex flex-col gap-y-8 py-12 px-6 bg-white shadow-lg overflow-hidden sm:rounded  md:w-[550px] "
                >
                    <AppLogoWithTextComponent />
                    <div
                        className="block text-center text-gray-700 text-2xl font-bold select-none"
                    >
                        Two Factor Authentication
                        {/* <FaLock/> */}
                    </div>
                    <div
                        className="flex flex-col justify-center items-center gap-y-4"
                    >
                        <TbLockCheck
                            className="h-24 w-24 text-primary"
                        />
                        <div
                            className="text-center"
                        >
                            Enhance your security by setting up two-factor authentication.
                        </div>
                    </div>
                    <div
                        className="flex flex-col items-center font-semi-bold grow select-none gap-y-8"
                    >
                        <div
                            className="flex space-between gap-x-4 items-center"
                        >
                            <AppToggleSwitchComponent
                                isChecked={stateToggleShowOptions == true}
                                onChangeEvent={() => setStateToggleShowOptions(!stateToggleShowOptions)}
                                isDisabled={user.enable_email_otp == true || user.enable_authenticator_app == true}
                            />
                            <div
                                className="font-bold"
                            >
                                Enable Two-Factor Authentication
                            </div>
                        </div>
                        {
                            stateToggleShowOptions == true
                                ?
                                <div
                                    className="flex flex-col space-between gap-y-4 items-center"
                                >
                                    <div
                                        className="font-bold"
                                    >
                                        Setup Verification Method
                                    </div>
                                    <div
                                        className="flex flex-col gap-y-4"
                                    >
                                        <PartialUserAccountSetupTwoFactorAuthToggleEmail />
                                        <PartialUserAccountSetupTwoFactorAuthToggleAuthApp
                                            secretKey={secretKey}
                                            qrCodeImageUrl={qrCodeImageUrl}
                                        />
                                    </div>
                                </div>
                                :
                                null
                        }
                    </div>

                    <div
                        className="flex flex-col items-center justify-end space-y-6"
                    >
                        <button
                            className="underline text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                            onClick={handleFinishSetup}
                        >
                            {
                                user.enable_email_otp == true || user.enable_authenticator_app == true ? 'Proceed' : "I'll handle this later"
                            }
                        </button>
                    </div>
                </div>
            </div>
        </UserLayout>
    );

};
