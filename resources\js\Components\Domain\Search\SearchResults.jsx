import PrimaryButton from "@/Components/PrimaryButton";
import CartCounterState from "@/State/CartCounterState";
import { useState } from "react";
import { toast } from "react-toastify";
import SearchDomainNow from "./SearchDomainNow";
import SearchNoDomainFound from "./SearchNoDomainFound";
import axios from "axios";

export default function SearchResults({ auth, searchResult, cart, setCart, fees }) {

    const { setCartCounter } = CartCounterState();

    const [isLoading, setIsLoading] = useState(false);

    const storeItemToCart = async (payload) => {
        axios.post(route("mycart.store"), payload)
            .then((response) => {
                setIsLoading(false);
                toast.success('Added to cart.');
                setCart([...cart, payload[0].name]);
                setCartCounter(cart.length + 1);
            })
            .catch((error) => {
                setIsLoading(false);
                setCart([...cart, payload[0].name]);
                toast.error(error.response.data.message ?? 'Something went wrong!');
            })
    };

    const onhandleAddToCart = (name) => {
        storeItemToCart([
            {
                name: name,
                extension: name.replace(/^[^\.]*\./, ""),
            },
        ]);
    };

    const getDomainPrice = (domain) => {
        const extension = (domain.name).split('.').pop();

        return domain.price ? domain.price : (fees[extension].price ?? 0)
    };

    if (searchResult == undefined) return <SearchDomainNow />;
    else if (searchResult.length == 0) return <SearchNoDomainFound />;

    const handleAddToCart = (domain) => {
        setIsLoading(true);

        if (domain.price) {
            const payload = {
                'user_id': auth.user.id,
                'tld_id': fees[domain.extension].tld_id,
                'name': domain.name,
                'price': domain.price,
                'is_fast_transfer': domain.fast_transfer ? 1 : 0,
                'vendor': 'afternic',
            };

            axios.post(route("mycart.market"), payload)
                .finally(() => {
                    setIsLoading(false);
                    toast.success('Added to cart.');
                    setCart(prev => [...prev, domain.name]);
                    setCartCounter(cart.length + 1);
                })
        } else {
            onhandleAddToCart(domain.name)
        }
    }

    // Basic Search

    return (
        <div className="mx-auto container max-w-[900px] flex flex-col">
            <h2 className="text-lg font-semibold text-gray-800 border-b border-gray-100 pb-4 mb-6">Search Results</h2>

            <div className="space-y-4">
                {searchResult.map((domain, index) => (
                    <div
                        key={"sri-" + index}
                        className="bg-white border border-gray-100 rounded-2xl p-6 flex justify-between items-center hover:border-gray-200 transition-all duration-200 shadow-sm"
                    >
                        <div className="flex-1">
                            <h3 className="text-lg font-medium text-gray-800">
                                <div className="flex">
                                    {domain.name}
                                    {domain.price && (
                                        <span className="ml-2 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex items-center">
                                            Premium
                                        </span>
                                    )}
                                    {domain.fast_transfer == 1 && (
                                        <span className="ml-2 bg-gradient-to-r bg-blue-50 text-primary text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm flex items-center">
                                            Fast Transfer
                                        </span>
                                    )}
                                </div>
                            </h3>
                            {domain.available ? (
                                <p className="text-xl font-semibold  mt-1.5">
                                    ${getDomainPrice(domain).toLocaleString('en', { useGrouping: true })}
                                </p>
                            ) : (
                                <p className="text-gray-500 mt-1.5 flex items-center">
                                    Not available
                                </p>
                            )}
                        </div>

                        {domain.available && !cart?.includes(domain.name) && (
                            <PrimaryButton
                                processing={isLoading}
                                onClick={() => handleAddToCart(domain)}
                                className="disabled:opacity-50 px-5 py-2.5  transition-all duration-200 font-medium flex items-center space-x-2"
                            >
                                <span>Add to Cart</span>
                            </PrimaryButton>
                        )}

                        {(cart?.includes(domain.name)) && (
                            <span className="block px-3.5 py-2.5 bg-gray-700 border border-transparent rounded-md font-semibold text-sm 
                            text-white tracking-widest bg-opacity-30">Added to cart</span>
                        )}
                    </div>
                ))}
            </div>
        </div>
    );
}