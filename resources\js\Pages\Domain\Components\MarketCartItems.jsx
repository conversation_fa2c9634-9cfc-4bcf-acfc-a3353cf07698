import SecondaryButton from "@/Components/SecondaryButton";
import React from "react";
import { MdAppRegistration } from "react-icons/md";
import { BsTrash } from "react-icons/bs";
import { toast } from "react-toastify";
import axios from "axios";

export default function MarketCartItems({ item, renew, items, setItems }) {
    const onDeleteItem = async (id) => {
        await axios
            .post(route("mycart.market.delete"), { id: id })
            .finally(() => {
                toast.success("Domain has been removed from cart.");
                setItems(
                    items.filter((a) => {
                        return a.id != item.id;
                    })
                );
            });
    };

    const getRenewPrice = (domain) => {
        return renew[domain.name.split(".").pop()].price;
    };

    const getFormattedPrice = (price) => {
        return Number(parseFloat(price).toFixed(2)).toLocaleString("en", {
            useGrouping: true,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
    };


    return (
        <div className={`space-y-2`} id={`market-${item.id}`}>
            <div className="flex items-center justify-between text-gray-600 border-b border-gray-200 pb-2 mb-4 group">
                <div>
                    <label className=" text-2xl">{item.name}</label>
                    <span className="ml-3 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm ">
                        Premium
                    </span>
                    {item.is_fast_transfer == 1 && (
                        <span className="ml-1 bg-blue-50 text-primary font-semibold text-xs px-2 rounded-full">
                            Fast Transfer
                        </span>
                    )}
                </div>
                <button onClick={() => onDeleteItem(item.id)}>
                    <BsTrash className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer opacity-0 group-hover:opacity-100" />
                </button>
            </div>
            <div className="flex items-center h-full align-bottom  justify-between hover:bg-gray-50 relative">
                <div className="flex items-center">
                    <MdAppRegistration className=" text-lg" />
                    <span className="ml-2 text-md text-gray-600">
                        {" "}
                        Domain Price{" "}
                    </span>
                </div>
                <div className={`inline-flex items-center px-4 py-2 bg-white border-gray-300 rounded-md  text-gray-700 tracking-widest hover:bg-gray-50 focus:outline-none focus:ring-2  focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150 uppercase`}>
                    <span className='text-md'>
                        ${getFormattedPrice(item.price)}
                    </span>
                </div>
            </div>
            <div className="flex items-center justify-between !-mt-2 hover:bg-gray-50 relative">
                <div className="flex items-center">
                    <MdAppRegistration className=" text-lg" />
                    <span className="ml-2 text-md text-gray-700"> Transfer Fee </span>
                </div>
                <div className={`inline-flex items-center px-4 py-2 bg-white border-gray-300 rounded-md  text-gray-700 tracking-widest hover:bg-gray-50 focus:outline-none focus:ring-2  focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150 uppercase`}>
                    <span className='text-md'>
                        ${getFormattedPrice(getRenewPrice(item))}
                    </span>
                </div>
            </div>
        </div>
    );
}
