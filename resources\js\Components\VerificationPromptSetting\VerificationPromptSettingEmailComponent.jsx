//* PACKAGES
import React, { useState } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';
import AppPromptEmailVerificationComponent from "@/Components/App/AppPromptEmailVerificationComponent";

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function VerificationPromptSettingEmailComponent(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    const [stateModalShowPromptEmailVerification, setStateModalShowPromptEmailVerification] = useState(false);

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    function handleSubmit() {
        router.patch(
            route('user-verification.prompt.set'),
            {
                value: 'email',
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('Verification prompt set.')
                },
                onError: () => {
                    toast.success('Something went wrong!')
                }
            }
        );
    }

    function handleError() {
        toast.error('Something went wrong!')
    }

    return (
        <div
            className='flex justify-between items-center'
        >
            <AppPromptEmailVerificationComponent
                show={stateModalShowPromptEmailVerification}
                onClose={() => setStateModalShowPromptEmailVerification(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleSubmit()}
                onSubmitError={() => handleError()}
            />

            <div
                className='flex flex-col gap-y-4  select-none'
            >
                <div
                    className=''
                >
                    Email
                </div>
            </div>
            <div>
                <AppToggleSwitchComponent
                    isChecked={user.verification_prompt == 'email'}
                    isDisabled={user.verification_prompt == 'email'}
                    onChangeEvent={
                        () => {
                            setStateModalShowPromptEmailVerification(true);
                        }
                    }
                />
            </div>
        </div>
    );
}
