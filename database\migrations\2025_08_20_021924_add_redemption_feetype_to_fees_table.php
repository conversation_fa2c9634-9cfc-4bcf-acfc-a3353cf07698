<?php

use App\Modules\Setting\Constants\FeeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            SELECT setval(
                pg_get_serial_sequence('fees', 'id'),
                COALESCE((SELECT MAX(id) FROM fees), 0) + 1,
                false
            )
        ");

        $feeId = DB::table('fees')->insertGetId([
            'type'       => FeeType::REDEMPTION,
            'value'      => 100,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $extensions = DB::table('extensions')->pluck('id');

        $rows = [];
        $now = now();

        foreach ($extensions as $extId) {
            $rows[] = [
                'extension_id' => $extId,
                'fee_id'       => $feeId,
                'value'        => 0,
                'is_default'   => true,
                'user_id'      => 0,
                'created_at'   => $now,
                'updated_at'   => $now,
            ];
        }

        DB::table('extension_fees')->insert($rows);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $fee = DB::table('fees')->where('type', FeeType::REDEMPTION)->first();

        if ($fee) {
            DB::table('extension_fees')->where('fee_id', $fee->id)->delete();
            DB::table('fees')->where('id', $fee->id)->delete();
        }
    }
};
