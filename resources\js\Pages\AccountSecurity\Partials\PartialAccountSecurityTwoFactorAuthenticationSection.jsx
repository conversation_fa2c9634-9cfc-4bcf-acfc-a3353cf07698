//* PACKAGES 
import React, { useState, useEffect } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
import { MdEdit, MdClose } from 'react-icons/md';
import { FiTrash } from 'react-icons/fi';

//* LAYOUTS
//...

//* COMPONENTS
import AuthAppModalFormRecoveryCodesComponent from '@/Components/AuthApp/AuthAppModalFormRecoveryCodesComponent';
import AuthAppModalSetupFormComponent from '@/Components/AuthApp/AuthAppModalFormSetupComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';
import AppPromptAuthenticatorAppVerificationComponent from '@/Components/App/AppPromptAuthenticatorAppVerificationComponent';
import PrimaryButton from '@/Components/PrimaryButton';
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';

//* STATE
import UtilGenerateRandomString from '@/Util/UtilGenerateRandomString';

//* UTILS
//... 

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//... 

export default function PartialAccountSecurityTwoFactorAuthenticationSection(
    {
        //! VARIABLES
        secretKey,
        qrCodeImageUrl,
        recoveryCodeInformation

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    const [stateModalShowPasswordVerificationPrompt, setStateModalShowPasswordVerificationPrompt] = useState(false);
    const [stateModalPasswordVerificationPromptSubmitEvent, setStateModalPasswordVerificationPromptSubmitEvent] = useState('');
    const [stateModalPasswordVerificationPromptErrorEvent, setStateModalPasswordVerificationPromptErrorEvent] = useState('');
    const [stateModalOpenFormAuthenticatorApp, setStateModalOpenFormAuthenticatorApp] = useState(false);
    const [stateModalOpenRecoveryCode, setStateModalOpenRecoveryCode] = useState(false);

    const [stateModalShowPromptAuthenticatorAppVerification, setStateModalShowPromptAuthenticatorAppVerification] = useState(false);

    const [stateInputRecoveryCodes, setStateInputRecoveryCodes] = useState(Array.from({ length: 5 }, () => UtilGenerateRandomString(12, 'alphanumeric', true)));


    //! USE EFFECTS

    //! FUNCTIONS
    function handleSubmitSuccessPasswordVerification() {
        switch (stateModalPasswordVerificationPromptSubmitEvent) {
            case 'enableEmailOtp':
                handleEnableEmailOtp();
                break;

            case 'disableEmailOtp':
                handleDisableEmailOtp();
                break;

            case 'openActivateAuthenticatorAppForm':
                setStateModalOpenFormAuthenticatorApp(true);
                break;


            case 'authenticatorAppDisable':
                handleDisableAuthenticatorApp();
                break;
        }
    }

    //! FUNCTIONS
    function handleSubmitErrorPasswordVerification() {
        // alert('error');
        //...
    }

    function handleEnableEmailOtp() {
        router.patch(
            route("user-settings.auth.email.enable"),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Email authentication enabled.");
                }
            }
        );
    }

    function handleDisableEmailOtp() {
        router.patch(
            route("user-settings.auth.email.disable"),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Email authentication disabled.");
                }
            }
        );
    }


    function handleDisableAuthenticatorApp() {
        router.patch(
            route('user-settings.authenticator-app.disable'),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Authenticator app authentication disabled.");
                }
            }
        );
    }

    function handleRecoverCodeRegeneration() {
        router.patch(
            route('user-settings.authenticator-app.recovery-code.regenerate'),
            {
                recoveryCodes: stateInputRecoveryCodes
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Recovery codes regenerated.");
                    setStateModalOpenRecoveryCode(true);
                },
                onError: (e) => {
                    if (e.rateLimitMessage) {
                        toast.error(e.rateLimitMessage);
                    }
                    else {
                        toast.error('Something went wrong!');
                    }
                },
            },
        )
    }

    function subComponentSetupEmail() {
        return (
            <div
                className='flex justify-between'
            >
                <div
                    className='flex flex-col gap-y-4 '
                >
                    <div
                        className='font-semibold'
                    >
                        Email Authentication
                    </div>
                    <div
                        className='text-gray-500'
                    >
                        Use your Email Address to send a Verification Link containing a code
                    </div>
                </div>
                <div>
                    <AppToggleSwitchComponent
                        isChecked={user.enable_email_otp}
                        onChangeEvent={
                            () => {
                                setStateModalShowPasswordVerificationPrompt(true);
                                if (user.enable_email_otp == true) {
                                    setStateModalPasswordVerificationPromptSubmitEvent('disableEmailOtp')
                                }
                                else {
                                    setStateModalPasswordVerificationPromptSubmitEvent('enableEmailOtp')
                                }

                            }
                        }
                    />
                </div>
            </div>
        );
    }

    function subComponentSetupAuthApp() {
        return (
            <div
                className='flex justify-between items'
            >
                <div
                    className='flex flex-col gap-y-4 '
                >
                    <div
                        className='font-semibold'
                    >
                        Authenticator App
                    </div>
                    <div
                        className='text-gray-500'
                    >
                        Use an Authenticator App to get a verification code when you log in
                    </div>
                </div>
                {
                    user.enable_authenticator_app == false
                        ?
                        <div>
                            <PrimaryButton
                                onClick={
                                    () => {
                                        setStateModalShowPasswordVerificationPrompt(true);

                                        if (user.enable_authenticator_app == false) {
                                            setStateModalPasswordVerificationPromptSubmitEvent('openActivateAuthenticatorAppForm')
                                        }
                                    }
                                }
                            >
                                Setup
                            </PrimaryButton>
                        </div>
                        :
                        <div
                            className='flex self-start gap-x-2 '
                        >
                            <PrimaryButton
                                className=''
                                onClick={
                                    () => {
                                        setStateModalShowPasswordVerificationPrompt(true);

                                        setStateModalPasswordVerificationPromptSubmitEvent('openActivateAuthenticatorAppForm')
                                    }
                                }
                            >
                                <MdEdit
                                    className='h-5 w-5'
                                />
                            </PrimaryButton>
                            <PrimaryButton
                                className=''
                                onClick={
                                    () => {
                                        setStateModalShowPasswordVerificationPrompt(true);

                                        setStateModalPasswordVerificationPromptSubmitEvent('authenticatorAppDisable')
                                    }
                                }
                            >
                                <FiTrash
                                    className='h-5 w-5'
                                />
                            </PrimaryButton>
                        </div>
                }

            </div>
        );
    }

    function subComponentSetupRecoveryCodes() {
        return (
            <div
                className='flex justify-between'
            >
                <div
                    className='flex flex-col gap-y-4 '
                >
                    <div
                        className='font-semibold'
                    >
                        Recovery Codes
                    </div>
                    <div
                        className='text-gray-500'
                    >
                        <span>
                            {recoveryCodeInformation.used} out of {recoveryCodeInformation.total} of your recovery codes have been used.
                        </span>

                        {recoveryCodeInformation.total - recoveryCodeInformation.used <= 1 ? <span> Consider generating a new set. </span> : null}
                    </div>
                </div>
                <div>
                    <PrimaryButton
                        onClick={
                            () => {
                                setStateModalShowPromptAuthenticatorAppVerification(true);
                            }
                        }
                    >
                        Generate New Set
                    </PrimaryButton>
                </div>
            </div>
        );
    }

    return (
        <div
            className='flex flex-col gap-y-8 justify-between'
        >
            <AppPromptPasswordVerificationComponent
                show={stateModalShowPasswordVerificationPrompt}
                onClose={() => setStateModalShowPasswordVerificationPrompt(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleSubmitSuccessPasswordVerification()}
                onSubmitError={() => handleSubmitErrorPasswordVerification()}
            />

            <AppPromptAuthenticatorAppVerificationComponent
                show={stateModalShowPromptAuthenticatorAppVerification}
                onClose={() => setStateModalShowPromptAuthenticatorAppVerification(false)}
                onSubmitSuccess={() => handleRecoverCodeRegeneration()}
            />

            <AuthAppModalSetupFormComponent
                secretKey={secretKey}
                qrCodeImageUrl={qrCodeImageUrl}
                recoveryCodes={stateInputRecoveryCodes}
                maxWidth='2xl'
                isModalOpen={stateModalOpenFormAuthenticatorApp}
                handleModalClose={
                    (openModalRecoveryCodes = false) => {
                        setStateModalOpenFormAuthenticatorApp(false)
                        setStateModalOpenRecoveryCode(openModalRecoveryCodes);
                    }
                }
            />

            <AuthAppModalFormRecoveryCodesComponent
                isModalOpen={stateModalOpenRecoveryCode}
                recoveryCodes={stateInputRecoveryCodes}
                handleModalClose={() => setStateModalOpenRecoveryCode(false)}
            />

            <div
                className='flex flex-col gap-y-4 '
            >
                <div
                    className='font-semibold'
                >
                    Two Factor Authentication
                </div>
                <div
                    className='text-gray-500'
                >
                    Enable 2-FA to increase your security
                </div>
            </div>

            <div
                className="flex flex-col pl-10 gap-y-8"
            >
                {subComponentSetupEmail()}
                {subComponentSetupAuthApp()}
                {
                    user.enable_authenticator_app == false
                        ?
                        null
                        :
                        subComponentSetupRecoveryCodes()
                }
            </div>
        </div>
    );
}
