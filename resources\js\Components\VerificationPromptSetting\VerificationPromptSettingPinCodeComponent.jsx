//* PACKAGES
import React, { useState } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';
import AppPromptPinCodeVerificationComponent from "@/Components/App/AppPromptPinCodeVerificationComponent";

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function VerificationPromptSettingPinCodeComponent(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    const [stateModalShowPromptPinCodeVerification, setStateModalShowPromptPinCodeVerification] = useState(false);

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    function handleSubmit() {
        router.patch(
            route('user-verification.prompt.set'),
            {
                value: 'pinCode',
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('Verification prompt set.')
                },
                onError: () => {
                    toast.success('Something went wrong!')
                }
            }
        );
    }

    function handleError() {
        toast.error('Something went wrong!')
    }

    return (
        <div
            className='flex justify-between items-center'
        >
            <AppPromptPinCodeVerificationComponent
                show={stateModalShowPromptPinCodeVerification}
                onClose={() => setStateModalShowPromptPinCodeVerification(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleSubmit()}
                onSubmitError={() => handleError()}
            />

            <div
                className='flex flex-col gap-y-4 select-none'
            >
                <div
                    className=''
                >
                    Pin Code
                </div>
            </div>
            <div>
                <AppToggleSwitchComponent
                    isChecked={user.verification_prompt == 'pinCode'}
                    isDisabled={user.verification_prompt == 'pinCode'}
                    onChangeEvent={
                        () => {
                            setStateModalShowPromptPinCodeVerification(true);
                        }
                    }
                />
            </div>
        </div>
    );
}
