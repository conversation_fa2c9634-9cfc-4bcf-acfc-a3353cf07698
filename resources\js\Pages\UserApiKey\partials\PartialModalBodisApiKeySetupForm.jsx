//* PACKAGES
import React, { useRef, useState } from "react";
import { toast } from "react-toastify";
import { useForm } from 'laravel-precognition-react-inertia';

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import Modal from "@/Components/Modal";
import PrimaryButton from '@/Components/PrimaryButton';
import InputLabel from "@/Components/InputLabel";
import InputError from "@/Components/InputError";
import TextInput from "@/Components/TextInput";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialModalBodisApiKeySetupForm(
    {
        //! VARIABLES
        //...

        //! STATES 
        isModalOpen,

        //! EVENTS
        handleModalClose,
    }
) {
    //! PACKAGE    
    const form = useForm(
        'post',
        route("user-api-key.store"),
        {
            apiKey: '',
        }
    );

    //! STATES
    //const [stateInputApiKey, setStateInputApiKey] = useState('');

    //! VARIABLES
    //...

    //! FUNCTIONS 
    //...

    //! FUNCTIONS
    function handleSelfClose() {
        //* CLOSE MODAL
        form.clearErrors();
        form.reset();

        handleModalClose();
    }

    function handleSubmit(e) {
        e.preventDefault();

        form.clearErrors();

        form.submit(
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Bodis API key set.");
                    form.reset();

                    handleSelfClose();
                },
                onError: () => {
                    toast.error("Something went wrong!");
                }
            }
        );
    }

    return (
        <Modal
            show={isModalOpen}
            closeable={false}
            onClose={handleSelfClose}
            maxWidth='md'
        >
            <form
                onSubmit={handleSubmit}
                className={`
                    flex flex-col justify-around
                    px-10 pt-5 pb-10
                    gap-y-8
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex flex-col'
                >
                    <div
                        className="flex justify-end"
                    >
                        <button
                            type="button"
                            className="text-primary ease-in-out duration-100 hover:text-blue-900"
                            onClick={handleSelfClose}
                        >
                            <IoMdCloseCircle
                                className="h-8 w-8 "
                            />
                        </button>
                    </div>
                    <div
                        className="flex flex-col gap-y-5 select-none"
                    >
                        <div
                            className="text-center"
                        >
                            <span
                                className="p-3 bg-primary rounded-sm text-white font-bold text-3xl"
                            >
                                SD
                            </span>
                        </div>
                        <div
                            className="block text-center text-gray-700 text-xl"
                        >
                            Enter your Bodis API Key
                        </div>
                    </div>
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-4'
                >
                    <TextInput
                        value={form.data.apiKey}
                        name={'apiKey'}
                        placeholder={'Api Key'}
                        className="w-full text-center tracking-widest"
                        handleChange={e => form.setData('apiKey', e.currentTarget.value)}
                    />
                    <InputError
                        className="text-center"
                        message={form.errors.apiKey}
                    />
                </section>

                {/* SECTION FOOTER */}
                <section
                    className='flex justify-end gap-x-2'
                >
                    {/* <PrimaryButton
                        type='button'
                        onClick={handleSelfClose}
                    >
                        Close
                    </PrimaryButton> */}
                    <PrimaryButton
                        type='submit'
                        className="w-full"
                        processing={form.processing == true || form.data.apiKey.length == 0}
                    >
                        Submit
                    </PrimaryButton>
                </section>
            </form>
        </Modal>
    );
}
