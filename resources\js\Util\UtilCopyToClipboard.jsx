import { toast } from "react-toastify";

export default function UtilCopyToClipboard(valueToBeCopied, successfulMessage = "Copied to clipboard.") {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(valueToBeCopied)
            .then(
                () => {
                    toast.success(successfulMessage);
                }
            )
            .catch(
                (err) => {
                    console.error(err);
                }
            );
    }
    else {
        const textarea = document.createElement('textarea');
        textarea.value = valueToBeCopied;

        document.body.appendChild(textarea);

        textarea.style.position = 'absolute'; // Prevents the textarea from being visible
        textarea.style.opacity = '0';
        textarea.style.pointerEvents = 'none';

        textarea.select();
        textarea.setSelectionRange(0, textarea.value.length); // For mobile devices

        try {
            const successful = document.execCommand('copy');

            if (successful) {
                toast.success(successfulMessage);
            }
            else {
                toast.error("Could not copy due to being HTTP. Please use HTTPS.");
            }
        }
        catch (err) {
            toast.error("Could not copy due to being HTTP. Please use HTTPS.");
        }
    }
}