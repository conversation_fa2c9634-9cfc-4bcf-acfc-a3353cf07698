//* PACKAGES 
import React, { useState, useEffect } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";
import { useForm } from 'laravel-precognition-react-inertia';

//* ICONS
import { MdEdit, MdClose } from 'react-icons/md';
import { FiTrash } from 'react-icons/fi';

//* LAYOUTS
//...

//* COMPONENTS
import PrimaryButton from '@/Components/PrimaryButton';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';


//* STATE
//...

//* UTILS
//... 

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
import PartialModalPinCodeSetupForm from './PartialModalPinCodeSetupForm';

export default function PartialAccountSecurityPinCodeSection(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    const form = useForm(
        'patch',
        route("user-pin-code.delete")
    );

    //! STATES 
    const [stateModalShowPasswordVerificationPrompt, setStateModalShowPasswordVerificationPrompt] = useState(false);
    const [stateModalShowPinCodeSetupForm, setStateModalShowPinCodeSetupForm] = useState(false);
    const [stateModalPasswordVerificationPromptSubmitEvent, setStateModalPasswordVerificationPromptSubmitEvent] = useState('');

    //! USE EFFECTS


    //! FUNCTIONS
    function handleSubmitSuccessPasswordVerification() {
        switch (stateModalPasswordVerificationPromptSubmitEvent) {
            case 'openModalSetupForm':
                setStateModalShowPinCodeSetupForm(true);

                break;

            case 'removePinCode':
                form.submit(
                    {
                        preserveScroll: true,
                        onSuccess: () => {
                            toast.success('PIN code removed.')
                        },
                        onError: () => {
                            toast.success('Something went wrong!')
                        }
                    }
                );
                break;
        }
    }

    //! FUNCTIONS
    function handleSubmitErrorPasswordVerification() {
        // alert('error');
        //...
    }

    return (
        <div
            className='flex justify-between'
        >
            <AppPromptPasswordVerificationComponent
                show={stateModalShowPasswordVerificationPrompt}
                onClose={() => setStateModalShowPasswordVerificationPrompt(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleSubmitSuccessPasswordVerification()}
                onSubmitError={() => handleSubmitErrorPasswordVerification()}
            />

            <PartialModalPinCodeSetupForm
                isModalOpen={stateModalShowPinCodeSetupForm}
                handleModalClose={() => setStateModalShowPinCodeSetupForm(false)}
            />

            <div
                className='flex flex-col gap-y-4'
            >
                <div
                    className='font-semibold'
                >
                    PIN Code
                </div>
                <div
                    className='text-gray-500'
                >
                    Use a 6-Digit Code to secure your account
                </div>
            </div>
            <div>
                {
                    user.hasPinCode == false
                        ?
                        <PrimaryButton
                            onClick={
                                () => {
                                    setStateModalShowPasswordVerificationPrompt(true);

                                    setStateModalPasswordVerificationPromptSubmitEvent('openModalSetupForm')
                                }
                            }
                        >
                            Setup
                        </PrimaryButton>
                        :
                        <div
                            className='flex self-start gap-x-2'
                        >
                            <PrimaryButton
                                className=''
                                onClick={
                                    () => {
                                        setStateModalShowPasswordVerificationPrompt(true);

                                        setStateModalPasswordVerificationPromptSubmitEvent('openModalSetupForm');
                                    }
                                }
                            >
                                <MdEdit
                                    className='h-5 w-5'
                                />
                            </PrimaryButton>
                            <PrimaryButton
                                className=''
                                onClick={
                                    () => {
                                        setStateModalShowPasswordVerificationPrompt(true);

                                        setStateModalPasswordVerificationPromptSubmitEvent('removePinCode');
                                    }
                                }
                            >
                                <FiTrash
                                    className='h-5 w-5'
                                />
                            </PrimaryButton>
                        </div>
                }
            </div>
        </div>
    );
}
