//* PACKAGES
import React, { useEffect, useState } from 'react';
import { useForm, usePage, Link, router } from "@inertiajs/react";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from '@stripe/react-stripe-js';
import defaultTheme from 'tailwindcss/defaultTheme';

//* ICONS
import { MdKeyboardBackspace } from "react-icons/md";

//* COMPONENTS
import UserLayout from "@/Layouts/UserLayout";
import EmptyCart from "@/Components/Domain/Cart/EmptyCart";
import StripeCheckoutFormComponent from '@/Components/Stripe/StripeCheckoutFormComponent';
import CheckOutForm from '@/Components/AccountCredit/CheckOutForm';
import _PaymentSummary from '@/Constant/_PaymentSummary';
import PrimaryButton from '@/Components/PrimaryButton';
import Checkbox from '@/Components/Checkbox';
import RadioButton from '@/Components/RadioButton';

//* STATE
import CartCounterState from "@/State/CartCounterState";
import OrderTotal from '../../Components/Domain/Checkout/OrderTotal';
import StripeOrderTotal from '../../Components/Domain/Checkout/StripeOrderTotal';
import LoaderSpinner from '../../Components/LoaderSpinner';

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function MultiCheckout(
    {
        domains = [],
        market_domains = [],
        other_fees,
        secret,
        promise,
        intent,
        account_credit_balance = 0,
        stripeFeeObj = [],
    }
) {

    //! PACKAGE
    const user = usePage().props.auth.user;
    const stripePromise = loadStripe(promise);
    const stripeSecret = secret;
    const fontsArray = ["Figtree", ...defaultTheme.fontFamily.sans];
    const fonts = fontsArray.join(", ");
    const appearance =
    {
        theme: 'stripe',
        variables:
        {
            fontFamily: fonts,
            colorText: '#374151',
        }
    };
    const stripeOptions =
    {
        clientSecret: stripeSecret,
        appearance: appearance,
    }

    //! STATES 
    const { setCartCounter } = CartCounterState();
    const [paymentMethod, setPaymentMethod] = useState('');
    const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState(null);
    const [isAgree, setIsAgree] = useState(false);
    const [validationError, setValidationError] = useState('');
    const [isPageProcessing, setPageProcessing] = useState(false);

    //! USE EFFECTS
    useEffect(
        () => {
            window.history.pushState(null, '', window.location.href);
            window.onpopstate = () => {
                window.history.replaceState(null, '', router.get('domain'));
                window.location.reload();
            };
        },
        []
    );

    //! FUNCTIONS
    const handlePaymentMethodChange = (event) => {
        setPaymentMethod(event.target.value);
        if (event.target.value !== 'saved_card') {
            setSelectedPaymentMethodId(null);
        }
    };

    const handlePaymentMethodSelect = (methodId) => {
        setSelectedPaymentMethodId(methodId);
        setPaymentMethod('saved_card');
    };

    const handleAgreementChange = (event) => {
        setIsAgree(event.target.checked);
    };

    console.log("domains", domains);
    console.log("market_domains", market_domains);
    console.log("other_fees", other_fees);

    // if (domains.domains.length == 0 && market_domains.domains.length == 0) {
    //     return (
    //         <EmptyCart />
    //     );
    // }

    const getFee = (domain) => {
        return (domains.settings.registration_fees[domain.name.split('.').pop()].price * domain.year_length)
    }

    const getTransferFee = (domain) => {
        return market_domains.settings.transfer_fees[domain.split('.').pop()].price
    }

    const getICANNFee = () => {
        return other_fees.icann_fee
    }

    const getGrossAmount = (paymentMethod) => {
        switch (paymentMethod) {
            case 'saved_card':
                return stripeFeeObj.gross_amount ?? other_fees.bill_total;
            case 'stripe':
                return stripeFeeObj.gross_amount ?? other_fees.bill_total;
            case 'account_credit':
                return other_fees.bill_total;
            default:
                return other_fees.bill_total;
        }
    }

    const getDomainsCount = () => {
        const domains_count = domains.domains ? domains.domains.length : 0;
        const market_domains_count = market_domains.domains ? market_domains.domains.length : 0;
        return domains_count + market_domains_count;
    }

    return (
        <UserLayout hideNav={true} postRouteName={'domain.multicart.summary'}>
            {isPageProcessing && <>
                <div className={`
                                fixed top-0 left-0 w-full min-h-screen bg-gray-500/50 z-50
                                flex flex-col justify-around
                                items-center
                                px-10 pt-5 pb-5
                                gap-y-5
                            `}>
                    <LoaderSpinner />
                </div>
            </>}
            <div className="mx-auto container max-w-6xl py-12">
                <div className="grid grid-cols-12 gap-5">
                    <div className="col-span-7">
                        <div className="flex flex-col">
                            <div className="mb-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">
                                    Select payment method
                                </h2>
                                <div className="border-b border-gray-200 mb-4"></div>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center space-x-4">
                                    <div
                                        className={`flex-grow cursor-pointer transition-all duration-200 ${paymentMethod === 'saved_card' ? '' : 'opacity-50'}`}
                                        onClick={() => {
                                            const radioBtn = document.querySelector('input[type="radio"][value="saved_card"]');
                                            if (radioBtn) {
                                                radioBtn.click();
                                            }
                                            setPaymentMethod('saved_card');
                                        }}
                                    >
                                        <Elements
                                            stripe={stripePromise}
                                            options={stripeOptions}
                                        >
                                            <StripeCheckoutFormComponent
                                                type={'multicheckout'}
                                                user_id={user.id}
                                                domains={domains}
                                                market_domains={market_domains}
                                                other_fees={other_fees}
                                                intent={intent}
                                                paymentMethod={'saved_card'}
                                                isActive={paymentMethod === 'saved_card'}
                                                selectedPaymentMethodId={selectedPaymentMethodId}
                                                onPaymentMethodSelect={handlePaymentMethodSelect}
                                                stripeFeeObj={stripeFeeObj}
                                                onHandlePageProcessing={setPageProcessing}
                                            />
                                        </Elements>
                                    </div>
                                </div>

                                <div className="flex items-center space-x-4">
                                    <div
                                        className={`flex-grow cursor-pointer transition-all duration-200 ${paymentMethod === 'stripe' ? '' : 'opacity-50'}`}
                                        onClick={() => {
                                            const radioBtn = document.querySelector('input[type="radio"][value="stripe"]');
                                            if (radioBtn) {
                                                radioBtn.click();
                                            }
                                            setPaymentMethod('stripe');
                                        }}
                                    >
                                        <Elements
                                            stripe={stripePromise}
                                            options={stripeOptions}
                                        >
                                            <StripeCheckoutFormComponent
                                                type={'multicheckout'}
                                                user_id={user.id}
                                                domains={domains}
                                                market_domains={market_domains}
                                                other_fees={other_fees}
                                                intent={intent}
                                                paymentMethod={'stripe'}
                                                isActive={paymentMethod === 'stripe'}
                                                stripeFeeObj={stripeFeeObj}
                                                onHandlePageProcessing={setPageProcessing}
                                            />
                                        </Elements>
                                    </div>
                                    <div className="ml-4">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="stripe"
                                            checked={paymentMethod === 'stripe'}
                                            onChange={handlePaymentMethodChange}
                                            className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                        />
                                    </div>
                                </div>

                                <div className="flex items-center space-x-4">
                                    <div
                                        className={`flex-grow cursor-pointer transition-all duration-200 ${paymentMethod === 'account_credit' ? '' : 'opacity-50'}`}
                                        onClick={() => {
                                            const radioBtn = document.querySelector('input[type="radio"][value="account_credit"]');
                                            if (radioBtn) {
                                                radioBtn.click();
                                            }
                                            setPaymentMethod('account_credit');
                                        }}
                                    >
                                        <CheckOutForm
                                            user={user}
                                            domains={domains}
                                            market_domains={market_domains}
                                            other_fees={other_fees}
                                            type={'multicheckout'}
                                            availableCredit={account_credit_balance}
                                            isActive={paymentMethod === 'account_credit'}
                                            onHandlePageProcessing={setPageProcessing}
                                        />
                                    </div>
                                    <div className="ml-4">
                                        <input
                                            type="radio"
                                            name="payment_method"
                                            value="account_credit"
                                            checked={paymentMethod === 'account_credit'}
                                            onChange={handlePaymentMethodChange}
                                            className="rounded-full border-gray-300 text-gray-600 shadow-sm focus:ring-gray-500"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="col-span-5">
                        <div className="bg-gray-50 rounded-lg p-6">
                            <div className="flex items-center justify-between mb-6">
                                <div className="flex items-center space-x-2">
                                    <Link
                                        replace={true}
                                        preserveState={false}
                                        href={route("domain.mycart")}
                                        className="text-gray-400 hover:text-gray-600 transition-colors"
                                    >
                                        <MdKeyboardBackspace className="text-xl" />
                                    </Link>
                                    <h2 className="text-lg font-semibold text-gray-800">Order Summary</h2>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div className="pb-4 border-b border-gray-200">
                                    {
                                        domains.length == 0 ? <></> : <div className="flex flex-col items-left justify-between text-gray-600 mb-6">
                                            <span className="font-semibold">
                                                Registration
                                            </span>
                                            <div className='flex flex-col mt-1'>
                                                {
                                                    domains.domains.map((a, i) => {

                                                        return <div key={i + '-itema'} className='flex justify-between'>
                                                            <span className=''>{a.name}<span className='text-sm'> ({a.year_length} year{a.year_length > 1 ? 's' : ''})</span></span>
                                                            <span className='text-sm'>${parseFloat(getFee(a)).toFixed(2)}</span>
                                                        </div>
                                                    })
                                                }
                                            </div>
                                        </div>
                                    }
                                    {
                                        market_domains.length == 0 ? <></> : <div className="flex flex-col justify-between text-gray-600 mb-6">
                                            <span className="font-semibold">
                                                Products
                                            </span>
                                            <div className='flex flex-col mt-1 gap-3'>
                                                {
                                                    market_domains.domains.map((a, i) => {
                                                        return <div key={i + '-itema'} className='flex flex-col'>
                                                            <div className='flex justify-between items-center'>
                                                                <div className='flex items-center gap-2'>
                                                                    <span>{a.name}</span>
                                                                    <span className=" bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-medium border border-yellow-200 shadow-sm ">
                                                                        Premium
                                                                    </span>
                                                                    {a.is_fast_transfer == 1 && (
                                                                        <span className=" bg-blue-50 text-primary font-semibold text-xs px-2 rounded-full">
                                                                            Fast Transfer
                                                                        </span>
                                                                    )}
                                                                </div>
                                                                <span className='text-sm'>${Number(parseFloat(a.price).toFixed(2)).toLocaleString('en', { useGrouping: true, minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span>
                                                            </div>
                                                            <div className='flex justify-between text-xs'>
                                                                <span className='text-sm'>Transfer fee</span>
                                                                <span className='text-sm'>${parseFloat(getTransferFee(a.name)).toFixed(2)}</span>
                                                            </div>
                                                        </div>
                                                    })
                                                }
                                            </div>
                                        </div>
                                    }
                                    <div className="flex items-left justify-between text-gray-600 ">
                                        <span className="font-semibold text-[15px]"> ICANN Fee </span>
                                        <span className='text-sm'>${getICANNFee().toFixed(2)}</span>
                                    </div>
                                </div>
                                {(paymentMethod === 'saved_card' || paymentMethod === 'stripe') ?
                                    <StripeOrderTotal total={getGrossAmount(paymentMethod)} service_fee={stripeFeeObj.service_fee} /> :
                                    <OrderTotal total={getGrossAmount(paymentMethod)} />}
                                {validationError && (
                                    <div className="mb-4 text-sm text-red-600">
                                        {validationError}
                                    </div>
                                )}
                                <div className="mb-6">
                                    <label className="flex items-center">
                                        <Checkbox
                                            name="is_agree"
                                            value="is_agree"
                                            checked={isAgree}
                                            handleChange={handleAgreementChange}
                                        />
                                        <span className="ml-2 text-sm text-gray-600">
                                            I agree to the &nbsp;
                                            <a className="underline text-sm text-link" href={route("terms")} target="_blank">
                                                Terms and Conditions
                                            </a>
                                            &nbsp; of StrangeDomains.
                                        </span>
                                    </label>
                                </div>

                                <div>
                                    <PrimaryButton
                                        type='submit'
                                        className={(!isAgree ? "!bg-[#CBD5E1]" : "") + " w-full"}
                                        processing={!isAgree || isPageProcessing}
                                        onClick={(e) => {
                                            e.preventDefault();
                                            setValidationError('');

                                            if (!paymentMethod) {
                                                setValidationError('Please select a payment method to continue.');
                                                return;
                                            }

                                            if (paymentMethod === 'saved_card' && !selectedPaymentMethodId) {
                                                setValidationError('Please select a saved card or choose a different payment method.');
                                                return;
                                            }

                                            if (isAgree) {
                                                let activeForm;
                                                if (paymentMethod === 'saved_card' || paymentMethod === 'stripe') {
                                                    const forms = document.querySelectorAll('form');
                                                    forms.forEach(form => {
                                                        if (!form.classList.contains('opacity-50')) {
                                                            activeForm = form;
                                                        }
                                                    });
                                                } else if (paymentMethod === 'account_credit') {
                                                    activeForm = document.querySelector('form[data-payment-type="account_credit"]');
                                                }

                                                if (activeForm) {
                                                    activeForm.requestSubmit();
                                                }
                                            }
                                        }}
                                        enableHover={false}
                                    >
                                        <div className={(isAgree ? "text-white" : "text-black") + " w-full flex items-center justify-between"}>
                                            <span>Confirm Payment</span>
                                            <div className='flex flex-wrap items-center justify-center'>
                                                <span>
                                                    ${Number(parseFloat(getGrossAmount(paymentMethod)).toFixed(2)).toLocaleString('en', { useGrouping: true, minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                                </span>
                                                <span className={"bg-black/40 text-[0.65rem] py-1 px-2 rounded-md ml-2"}>
                                                    {(getDomainsCount() + " " + (getDomainsCount() > 1 ? "items" : "item"))}
                                                </span>
                                            </div>
                                        </div>
                                    </PrimaryButton>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </UserLayout>
    );
}
