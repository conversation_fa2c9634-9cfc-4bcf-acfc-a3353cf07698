<?php

namespace App\Listeners\Payment;

use App\Events\Payment\CreatePaymentSummaryEvent;
use App\Models\PaymentSummary;
use App\Modules\CustomLogger\Services\AuthLogger;
use Exception;

class CreatePaymentSummaryListener
{

    public function handle(CreatePaymentSummaryEvent $event)
    {
        try {
            $payload = [
                'user_id' => $event->userId,
                'type' => $event->type,
                'name' => $event->data['name'] ?? $event->type,
                'paid_amount' => $event->data['paid_amount'] ?? 0,
                'total_amount' => $event->data['total_amount'] ?? 0,
                'payment_invoice_id' => $event->data['payment_invoice_id'] ?? null,
                'payment_market_place_invoice_id' => $event->data['payment_market_place_invoice_id'] ?? null,
                'payment_service_id' => $event->data['payment_service_id'] ?? null,
                'source' => $event->data['source'] ?? null,
            ];

            PaymentSummary::create($payload);

            app(AuthLogger::class)->info('Payment summary created: '.$payload['name']);

        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }
}
