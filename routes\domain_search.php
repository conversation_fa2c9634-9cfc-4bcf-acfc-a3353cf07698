<?php

use App\Modules\DomainEppSearch\Controllers\SearchController;
use App\Modules\Search\Controllers\MainSearchController;
use Illuminate\Support\Facades\Route;

Route::middleware(
        [
            'auth',
            'auth.active',
            'account.setup'
        ]
    )
    ->prefix('domain')
    ->group(
        function () {
            Route::get('/search', [SearchController::class, 'view'])->name('domain.search');

            Route::middleware(['throttle:15,1'])->group(
                function () {
                    Route::post('/search', [SearchController::class, 'check'])->name('domain.check');
                    Route::post('/aisearch', [SearchController::class, 'aiSearch'])->name('search.ai');
                    Route::post('/marketsearch', [SearchController::class, 'marketSearch'])->name('domain.search.market');
                    Route::post('/basicsearch', [SearchController::class, 'basicSearch'])->name('domain.search.basic');
                }
            );
        }
    );
