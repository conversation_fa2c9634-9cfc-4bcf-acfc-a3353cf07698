//* PACKAGES 
import React, { useRef, useState, useEffect } from "react";
import { toast } from "react-toastify";
import { Head, router } from "@inertiajs/react";
import { useForm } from "laravel-precognition-react-inertia";

//* ICONS
//...

//* LAYOUTS 
import GuestLayout from "@/Layouts/GuestLayout";

//* COMPONENTS
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";

//* STATE
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function Login2FaVerificationEmail(
    {
        token
    }
) {
    const [resendButtonDisabled, setResendButtonDisabled] = useState(false);
    const [resendCodeButtonTimerDisabled, setResendCodeButtonTimerDisabled] = useState(0);
    const [isCodeContainerActive, setIsCodeContainerActive] = useState(false);

    const refCodeInput = useRef(null);
    const refInputs = useRef([]);
    const refCodeContainer = useRef(null);

    useEffect(
        () => {
            const handleClickOutside = (event) => {
                if (!refCodeContainer.current.contains(event.target)) {
                    for (let i = 0; i < refInputs.current.length; i++) {
                        refInputs.current[i].classList.remove('border-blue-400');
                        refInputs.current[i].classList.add('border-gray-400');
                    }

                    setIsCodeContainerActive(false);
                }
            };

            document.addEventListener('click', handleClickOutside);

            return () => {
                document.removeEventListener('click', handleClickOutside);
            };
        },
        [isCodeContainerActive]
    );

    function startResendButtonDisabledTimer() {
        setResendButtonDisabled(true);
        setResendCodeButtonTimerDisabled(120);

        const countdown = setInterval(
            () => {
                setResendCodeButtonTimerDisabled(
                    (prev) => {
                        if (prev <= 1) {
                            clearInterval(countdown);

                            setResendButtonDisabled(false);

                            return 0;
                        }

                        return prev - 1;
                    }
                );
            },
            1000
        );
    }

    function eventOnClickResendCode(e) {
        form.clearErrors();

        e.preventDefault();

        router.post(
            route('login.2fa.email.resend', token),
            {
            },
            {
                preserveScroll: true,
                preserveState: true,
                onSuccess: () => {
                    toast.info("Code resent. Check your email.");

                    //! Disable the button and start the countdown
                    startResendButtonDisabledTimer();
                },
                onError: (e) => {
                    if (e.rateLimitMessage) {
                        startResendButtonDisabledTimer();
                        toast.error(e.rateLimitMessage, { autoClose: 5000 });
                    }
                    else {
                        toast.error('Something went wrong!');
                    }
                },
            }
        )
    }

    function eventOnInputOtp(e) {
        const value = e.currentTarget.value;

        if (value.length != 0) {
            if (isNaN(value)) {
                return;
            }
        }

        if (value.length <= 6) {
            form.setData('code', value);

            for (let i = 0; i < refInputs.current.length; i++) {
                refInputs.current[i].innerText = value[i] ?? '';
            }
        }

        //console.table(value, value.length, value[5]);
    }

    const form = useForm(
        'post',
        route("login.2fa.email.verify", { token }),
        {
            code: '',
        }
    );


    return (
        <GuestLayout>
            <Head title="Login 2FA Email" />

            <div
                className="flex flex-col justify-around gap-y-12 py-5"
            >
                <div
                    className="block text-center text-gray-700 text-2xl"
                >
                    Verify Your Account
                </div>
                <form
                    className="flex flex-col gap-y-12 items-center grow"
                    onSubmit={
                        (e) => {
                            e.preventDefault();

                            form.clearErrors();

                            form.submit();
                        }

                    }
                >
                    <div
                        className="grow select-none text-center"
                    >
                        We have sent the verification code to your Email Address.
                    </div>

                    <div
                        className="flex-flex-col gap-y-2"
                    >
                        <div
                            className="flex flex-col gap-8"
                        >
                            <InputError message={form.errors.code} className="text-center" />
                            <div
                                ref={refCodeContainer}
                                className="flex gap-2"
                                onClick={
                                    () => {
                                        setIsCodeContainerActive(true);

                                        for (let i = 0; i < refInputs.current.length; i++) {
                                            refInputs.current[i].classList.remove('border-gray-400');
                                            refInputs.current[i].classList.add('border-blue-400');
                                        }

                                        refCodeInput.current.focus()
                                    }
                                }
                            >
                                {
                                    [...Array(6).fill(1)].map(
                                        (item, index) => {
                                            return (
                                                <div
                                                    key={index}
                                                    ref={(e) => refInputs.current[index] = e}
                                                    className="flex justify-center items-center w-14 h-14 rounded-lg cursor-pointer ease-in duration-200 border-gray-400 border text-primary text-2xl content-center"
                                                />
                                            )
                                        }
                                    )
                                }
                            </div>
                            <button
                                type='button'
                                className={`${resendButtonDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-primary ease-in hover:text-blue-500'}`}
                                onClick={(e) => eventOnClickResendCode(e)}
                                disabled={resendButtonDisabled}
                            >
                                Resend Code {resendButtonDisabled && <span> in {resendCodeButtonTimerDisabled}s</span>}
                            </button>
                        </div>
                        <input
                            ref={refCodeInput}
                            className="absolute w-0 h-0 p-0 b-0 focus:border-none focus:ring-0 border-0"
                            value={form.data.code}
                            type="text"
                            id="codeInput"
                            name="codeInput"
                            autoComplete="off"
                            onChange={(e) => eventOnInputOtp(e)}
                        />
                    </div>

                    <PrimaryButton
                        type="submit"
                        className="w-full"
                    >
                        Verify
                    </PrimaryButton>

                </form>
                <div className="flex flex-col items-center justify-end mt-4 space-y-6">
                    <div className="flex flex-col items-center justify-end space-y-1">
                        <button
                            className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                            onClick={() => window.history.back()}
                        >
                            Try Another Method
                        </button>
                    </div>
                </div>
            </div>
        </GuestLayout>
    );
}
