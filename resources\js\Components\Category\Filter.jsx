import ActiveFilter from "@/Components/Util/Filter/ActiveFilter";
import DisplayFilter from "@/Components/Util/Filter/DisplayFilter";
import OptionFilter from "@/Components/Util/Filter/OptionFilter";
import SearchNameFilter from "@/Components/Util/Filter/SearchNameFilter";
import useOutsideClick from "@/Util/useOutsideClick";
import { useRef, useState } from "react";
import {
    offFilter,
    updateFieldValue,
} from "@/Components/Util/Filter/FilterMethod";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";

export default function Filter() {
    const { orderby, category } = route().params;

    const config = {
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: [
                    "created:desc",
                    "created:asc",
                    "name:desc",
                    "name:asc",
                    "updated:desc",
                    "updated:asc",
                ],
                name: "Order By",
            },
        },
    };

    const [filter, setFilter] = useState(config);
    const [searchValue, setSearchValue] = useState(category || "");
    const [hasSearch, setHasSearch] = useState(!!category);
    const ref = useRef();
    const { field } = filter;

    useOutsideClick(ref, (event) => {
        if (filter.container.active) {
            setFilter({
                ...filter,
                container: { ...filter.container, active: false },
            });
        }

        const updatedField = { ...field };
        let hasChanges = false;

        Object.keys(updatedField).forEach((key) => {
            if (updatedField[key].active) {
                const dropdownElement = document.querySelector(
                    `[data-filter-key="${key}"]`
                );
                if (!dropdownElement?.contains(event.target)) {
                    updatedField[key].active = false;
                    hasChanges = true;
                }
            }
        });

        if (hasChanges) {
            setFilter({
                ...filter,
                field: updatedField,
            });
        }
    });

    const submit = (updatedFilter) => {
        let { orderby } = updatedFilter.field;
        let payload = {};

        if (orderby.value.length > 0) payload.orderby = orderby.value[0];
        if (searchValue.trim()) payload.category = searchValue.trim();

        router.get(route("category"), payload);
    };

    const handleDisplayToggle = (newObject) => {
        setFilter({ ...filter, ...newObject });
    };

    const handleFieldUpdateValue = (key, value) => {
        const newValue = updateFieldValue(value, { ...filter.field[key] });

        const reload = !(newValue.value.length === 0 && value !== "");

        const updatedFilter = {
            ...filter,
            container: { ...filter.container, active: false, reload: reload },
            field: {
                ...filter.field,
                [key]: { ...newValue },
            },
        };

        setFilter(updatedFilter);

        if (reload) {
            toast.info("Reloading data, please wait...");
            submit(updatedFilter);
        }
    };

    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchValue(value);

        if (!value.trim()) {
            handleClearSearch();
        }
    };

    const handleSearchSubmit = () => {
        const searchTerm = searchValue.trim().toLowerCase();

        if (!searchTerm) {
            toast.error("Please enter a category name to search.");
            return;
        }

        let payload = {
            category: searchTerm,
        };

        if (field.orderby.value.length > 0) {
            payload.orderby = field.orderby.value[0];
        }

        router.get(route("category"), payload, { preserveState: true });
        setHasSearch(true);
        toast.info("Searching categories, please wait...");
    };

    const handleClearSearch = () => {
        setSearchValue("");
        setHasSearch(false);

        let payload = {};
        if (field.orderby.value.length > 0) {
            payload.orderby = field.orderby.value[0];
        }

        router.get(route("category"), payload, { preserveState: true });
    };

    const handleOutsideClick = () => {
        if (searchValue.trim() && !hasSearch) {
            handleSearchSubmit();
        }
    };

    return (
        <div className="flex items-center justify-between w-full">
            <div className="flex items-center" ref={ref}>
                <ActiveFilter
                    field={field}
                    handleFieldUpdateValue={handleFieldUpdateValue}
                />
                <div>
                    <DisplayFilter
                        handleDisplayToggle={handleDisplayToggle}
                        container={filter.container}
                        field={field}
                    />
                    <div className="relative">
                        <OptionFilter
                            fieldProp={field.orderby}
                            fieldKey="orderby"
                            handleFieldUpdateValue={handleFieldUpdateValue}
                            data-filter-key="orderby"
                        />
                    </div>
                </div>
            </div>
            <div className="ml-auto">
                <SearchNameFilter
                    value={searchValue}
                    onChange={handleSearch}
                    onClear={handleClearSearch}
                    onSubmit={handleSearchSubmit}
                    onOutsideClick={handleOutsideClick}
                    placeholder="Search category name..."
                    hasSearch={hasSearch}
                />
            </div>
        </div>
    );
}
