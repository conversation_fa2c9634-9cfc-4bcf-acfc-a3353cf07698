//* PACKAGES
import React, { useState, useEffect } from 'react';
import { toast } from "react-toastify";
import { usePage } from '@inertiajs/react';
import { useForm } from 'laravel-precognition-react-inertia';
import { Transition } from '@headlessui/react';
import { router } from '@inertiajs/react';

//* ICONS
//...

//* COMPONENTS
import PrimaryButton from '@/Components/PrimaryButton';
import TextInput from '@/Components/TextInput';
import InputError from '@/Components/InputError';
import AppButtonLinkComponent from '@/Components/App/AppButtonLinkComponent';

//* PARTIALS
import PartialModalAuthenticatorAppRecoveryCodes from './PartialModalAuthenticatorAppRecoveryCodes';

//* STATE
//...

//* UTILS
import UtilGenerateRandomString from '@/Util/UtilGenerateRandomString';

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialFormUpdateAuthenticationAuthenticatorApp(
    {
        className,
        secretKey,
        qrCodeImageUrl,
        recoveryCodeInformation
    }
) {
    const user = usePage().props.auth.user;

    //! STATES 
    const [stateSecretKey, setStateSecretKey] = useState(secretKey);
    const [stateQrCodeImageUrl, setStateQrCodeImageUrl] = useState(qrCodeImageUrl);
    const [stateInputRecoveryCodes, setStateInputRecoveryCodes] = useState(Array.from({ length: 5 }, () => UtilGenerateRandomString(12, 'alphanumeric', true)));
    const [stateModalOpenRecoveryCodes, setStateModalOpenRecoveryCodes] = useState(false);

    const formEnable = useForm(
        'patch',
        route("user-settings.authenticator-app.enable"),
        {
            secretKey: stateSecretKey,
            inputCode: '',
            recoveryCodes: stateInputRecoveryCodes,
        }
    );

    const formDisable = useForm(
        'patch',
        route('user-settings.authenticator-app.disable')
    )

    //! FUNCTIONS
    function handleSubmitEnable(e) {
        e.preventDefault();

        formEnable.clearErrors();

        formEnable.submit(
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Authenticator app authentication enabled.");
                    formEnable.reset();

                    setStateModalOpenRecoveryCodes(true);
                },
                onError: (e) => {
                    console.log(e);
                    formEnable.reset();
                },
            }
        );
    };

    function handleSubmitDisable(e) {
        e.preventDefault();

        formDisable.submit(
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Authenticator app authentication disabled.");
                },
            }
        );
    };

    function handleRegenerateRecoveryCodes(e) {
        e.preventDefault();

        router.patch(
            route('user-settings.authenticator-app.recovery-code.regenerate'),
            {
                recoveryCodes: formEnable.data.recoveryCodes
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Recovery codes regenerated.");
                    setStateModalOpenRecoveryCodes(true);
                },
                onError: (e) => {
                    if (e.rateLimitMessage) {
                        toast.error(e.rateLimitMessage);
                    }
                    else {
                        toast.error('Something went wrong!');
                    }
                },
            },
        )
    }

    return (
        <section
            className={className}
        >
            <header>
                <h2
                    className="text-lg font-medium text-gray-900"
                >
                    Authenticator App  Settings
                </h2>
                <p
                    className="my-2 text-sm text-gray-600"
                >
                    Manage your account's authenticator app settings
                </p>
            </header>
            {
                user.enable_authenticator_app == true
                    ?
                    <form
                        className={`
                                    flex flex-col gap-y-4 mt-5`
                        }
                        onSubmit={handleSubmitDisable}
                    >
                        <div
                            className='text-sm text-slate-500 font-semibold'
                        >
                            <span>
                                {recoveryCodeInformation.used} out of {recoveryCodeInformation.total} of your recovery codes have been used.
                            </span>

                            {recoveryCodeInformation.total - recoveryCodeInformation.used <= 1 ? <span> Consider generating a new set. </span> : null}
                        </div>
                        <div
                            className="flex items-center gap-4"
                        >
                            <PrimaryButton
                                disabled={formDisable.processing}
                            >
                                Disable
                            </PrimaryButton>
                            <PrimaryButton
                                disabled={formDisable.processing}
                                onClick={handleRegenerateRecoveryCodes}
                            >
                                Generate New Set of Recovery Codes
                            </PrimaryButton>
                            <Transition
                                show={formDisable.recentlySuccessful}
                                enterFrom="opacity-0"
                                leaveTo="opacity-0"
                                className="transition ease-in-out"
                            >
                                <p className="text-sm text-gray-600">Saved.</p>
                            </Transition>
                        </div>
                    </form>
                    :
                    <form
                        className={`
                                    flex flex-col gap-y-2 mt-5`
                        }
                        onSubmit={handleSubmitEnable}
                    >
                        <div
                            className={`
                                        text-sm
                                        ${user.enable_authenticator_app == false ? 'flex flex-col' : 'hidden'}
                                    `}
                        >
                            <section>
                                <div
                                    className=''
                                >
                                    Use the Google Authenticator App to scan the QrCode below then enter the generated code. There is a 30 second window for submitting each valid code.
                                </div>
                                <img
                                    src={stateQrCodeImageUrl}
                                />
                            </section>
                            <section
                                className='flex flex-col gap-y-8'
                            >
                                <div
                                    className='flex flex-col gap-y-1'
                                >
                                    <TextInput
                                        name="inputCode"
                                        type="password"
                                        className="block w-full"
                                        maxLength={6}
                                        placeholder="Enter Generated Code"
                                        value={formEnable.data.inputCode}
                                        handleChange={(e) => { formEnable.setData('inputCode', e.target.value); }}
                                    />
                                    <InputError
                                        message={formEnable.errors.inputCode}
                                    />
                                </div>
                                <div
                                    className="flex items-center gap-4"
                                >
                                    <PrimaryButton
                                        type='submit'
                                        disabled={formEnable.processing}
                                    >
                                        Verify
                                    </PrimaryButton>
                                    <Transition
                                        show={formEnable.recentlySuccessful}
                                        enterFrom="opacity-0"
                                        leaveTo="opacity-0"
                                        className="transition ease-in-out"
                                    >
                                        <p className="text-sm text-gray-600">Saved.</p>
                                    </Transition>
                                </div>
                            </section>
                        </div>
                    </form>
            }
            <PartialModalAuthenticatorAppRecoveryCodes
                isModalOpen={stateModalOpenRecoveryCodes}
                recoveryCodes={formEnable.data.recoveryCodes}
                handleModalClose={() => setStateModalOpenRecoveryCodes(false)}
            />
        </section>
    );
}
