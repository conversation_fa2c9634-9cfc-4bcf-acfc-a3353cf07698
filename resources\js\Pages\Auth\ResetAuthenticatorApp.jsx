//* PACKAGES 
import React, { useRef, useState, useEffect } from "react";
import { Head, router } from "@inertiajs/react";
import { useForm } from "laravel-precognition-react-inertia";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS 
import GuestLayout from "@/Layouts/GuestLayout";

//* COMPONENTS
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";

//* STATE
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function Login2FaVerificationAuthenticatorApp(
    {
        token,
        secretKey,
        qrCodeImageUrl
    }
) {
    //! STATES
    const [stateSecretKey, setStateSecretKey] = useState(secretKey);
    const [stateQrCodeImageUrl, setStateQrCodeImageUrl] = useState(qrCodeImageUrl);

    const form = useForm(
        'post',
        route("login.2fa.authenticator-app.recovery-code.reset.verify", { token: token }),
        {
            secretKey: stateSecretKey,
            code: '',
        },
    );

    //! FUNCTIONS 
    function handleVerify(e) {
        e.preventDefault();

        form.clearErrors();

        form.submit(
            {
                onSuccess: () => {
                    toast.success("Authenticator app reset.");
                    form.reset();
                },
                onError: (e) => {
                    //toast.error("Something went wrong");
                },
            }
        );
    }

    function handleDisable(e) {
        e.preventDefault();

        router.patch(
            route('login.2fa.authenticator-app.recovery-code.reset.disable', { token: token }),
            {
                //... 
            },
            {
                replace: true,
                preserveState: false,
                onSuccess: () => {
                    toast.success('Authenticator app disabled.');
                },
                onError: () => {
                    toast.error('Something went wrong!');
                }
            }
        );
    }

    function handleSkip(e) {
        e.preventDefault();

        router.get(
            route('domain'),
            {
                //...
            },
            {
                replace: true,
            }
        )
    }

    return (
        <GuestLayout
            className='w-full sm:max-w-xl'
        >
            <Head title="Authenticator App Reset" />

            <form
                className="flex flex-col my-8 gap-y-4"
                onSubmit={handleVerify}
            >
                <section
                    className="text-sm text-slate-400 font-semibold text-center"
                >
                    We noticed that you used a recovery code. We encourage you to reset your authenticator app to prevent you from being locked from your account. Your unused recovery codes will still remain after resetting.
                </section>
                <section>
                    <div
                        className="flex flex-col items-center"
                    >
                        <img
                            className="w-80 h-80"
                            src={stateQrCodeImageUrl}
                        />
                    </div>
                    <div
                        className='flex flex-col gap-y-1'
                    >
                        <TextInput
                            name="inputCode"
                            type="password"
                            className="block w-full"
                            maxLength={6}
                            placeholder="Enter Generated Code"
                            value={form.data.inputCode}
                            handleChange={(e) => { form.setData('code', e.target.value); }}
                        />
                        <InputError
                            message={form.errors.code}
                        />
                    </div>
                </section>
                <section
                    className="flex flex-col gap-8"
                >
                    <div
                        className="flex flex-col gap-2"
                    >
                        <PrimaryButton
                            type='submit'
                            disabled={form.processing}
                        >
                            Verify
                        </PrimaryButton>
                        <PrimaryButton
                            type='button'
                            disabled={form.processing}
                            onClick={handleDisable}
                        >
                            Disable Method Instead
                        </PrimaryButton>
                    </div>
                    <button
                        type="button"
                        className="underline text-sm text-primary hover:text-gray-900 rounded-md focus:outline-none"
                        onClick={handleSkip}
                    >
                        I'll handle this later
                    </button>
                </section>
            </form>
        </GuestLayout>
    );
}