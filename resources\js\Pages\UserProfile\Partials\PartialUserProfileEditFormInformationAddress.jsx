//* PACKAGES 
import React, { useState, useEffect } from 'react';
import { router, usePage, useForm } from "@inertiajs/react";
import { toast } from "react-toastify";
import { Country, State, City } from "country-state-city";

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppSelectComponent from '@/Components/App/AppSelectComponent';
import InputLabel from '@/Components/InputLabel';
import InputError from '@/Components/InputError';
import TextInput from "@/Components/TextInput";
import PrimaryButton from '@/Components/PrimaryButton';

//* STATE
//...

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function PartialUserProfileEditFormInformationAddress(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    const form = useForm(
        'form',
        {
            unit: user.unit,
            street: user.street,
            city: user.city,
            stateProvince: user.state_province,
            postalCode: user.postal_code,
            countryCode: user.country_code
        }
    );

    const initialValues =
    {
        unit: user.unit,
        street: user.street,
        city: user.city,
        stateProvince: user.state_province,
        postalCode: user.postal_code,
        countryCode: user.country_code
    };

    //! STATES
    const [stateCountries, setStateCountries] = useState([]);
    const [stateProvinces, setStateProvinces] = useState([]);

    const [stateSelectedCountryCode, setSelectedCountryCode] = useState(form.data.countryCode ?? '');
    const [stateSelectedProvinceCode, setStateSelectedProvinceCode] = useState(
        () => {
            if (form.data.stateProvince == null) {
                return '';
            }

            const states = State.getStatesOfCountry(stateSelectedCountryCode);
            const state = states.find(s => s.name.toLowerCase() === form.data.stateProvince.toLowerCase());

            return state ? state.isoCode : '';
        }
    );

    //! USE EFFECTS
    useEffect(() => {
        const data = Country.getAllCountries();

        setStateCountries(
            data.map(
                (item) => {
                    return {
                        value: item.isoCode,
                        label: `${item.isoCode} - ${item.name}`
                    }
                }
            )
        );
    },
        []
    );

    useEffect(() => {
        const data = State.getStatesOfCountry(stateSelectedCountryCode);

        setStateProvinces(
            data.map(
                (item) => {
                    return {
                        value: item.isoCode,
                        label: `${item.name}`
                    }
                }
            )
        );
    },
        [
            stateSelectedCountryCode
        ]
    );

    //! VARIABLES
    const inputFields =
        [
            {
                label: 'street',
                type: 'text',
                name: 'street',
                placeholder: '123 Main St',
                value: form.data.street,
                errorMessage: form.errors.street,
                onBlur: (() => form.setData('street', form.data.street.trim())),
                eventOnChange: (e => form.setData('street', e.target.value)),
            },
            {
                label: 'apt, suite, etc. (optional)',
                type: 'text',
                name: 'unit',
                placeholder: 'Apt 302',
                value: form.data.unit,
                errorMessage: form.errors.unit,
                onBlur: (() => form.setData('unit', form.data.unit.trim())),
                eventOnChange: (e => form.setData('unit', e.target.value)),
            },
            {
                label: 'city',
                type: 'text',
                name: 'city',
                placeholder: 'New York',
                value: form.data.city,
                errorMessage: form.errors.city,
                onBlur: (() => form.setData('city', form.data.city.trim())),
                eventOnChange: (e => form.setData('city', e.target.value)),
            },
            // {
            //     label        : 'state / province',
            //     type         : 'text',
            //     name         : 'stateProvince',
            //     placeholder  : 'NY',
            //     value        : form.data.stateProvince,
            //     errorMessage : form.errors.stateProvince,
            //     onBlur       : (() => form.setData('stateProvince', form.data.stateProvince.trim())),
            //     eventOnChange: (e => form.setData('stateProvince', e.target.value)),
            // }, 
            {
                label: 'postal code',
                type: 'text',
                name: 'postalCode',
                placeholder: '10001',
                value: form.data.postalCode,
                errorMessage: form.errors.postalCode,
                onBlur: (() => form.setData('postalCode', form.data.postalCode.trim())),
                eventOnChange: (e => form.setData('postalCode', e.target.value)),
            },
            // {
            //     label        : 'country code',
            //     type         : 'text',
            //     name         : 'countryCode',
            //     placeholder  : 'US',
            //     value        : form.data.countryCode,
            //     errorMessage : form.errors.countryCode,
            //     onBlur       : (() => form.setData('countryCode', form.data.countryCode.trim())),
            //     eventOnChange: (e => form.setData('countryCode', e.target.value)),
            // },
        ];

    //! FUNCTIONS
    function handleSubmit(e) {
        e.preventDefault();

        form.patch(
            route("user-profile.update.address"),
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Profile address updated.");

                    handleSetInitialValues();

                    handleReset();
                },
            }
        );
    }

    function handleSetInitialValues() {
        initialValues.unit = form.data.unit,
            initialValues.street = form.data.street,
            initialValues.city = form.data.city,
            initialValues.stateProvince = form.data.stateProvince,
            initialValues.postalCode = form.data.postalCode,
            initialValues.countryCode = form.data.countryCode
    }

    function handleReset() {
        form.setData(
            {
                unit: initialValues.unit,
                street: initialValues.street,
                city: initialValues.city,
                stateProvince: initialValues.stateProvince,
                postalCode: initialValues.postalCode,
                countryCode: initialValues.countryCode
            }
        );
    }

    function checkIfFormIsDirty() {
        if (
            form.data.unit == initialValues.unit
            && form.data.street == initialValues.street
            && form.data.city == initialValues.city
            && form.data.stateProvince == initialValues.stateProvince
            && form.data.postalCode == initialValues.postalCode
            && form.data.countryCode == initialValues.countryCode
        ) {
            return false;
        }
        else {
            return true;
        }
    }

    function handleOnChangeCountry(event) {
        const updatedValue = getEventValue(event);
        const country = Country.getCountryByCode(updatedValue);

        setSelectedCountryCode(updatedValue);
        setStateSelectedProvinceCode('')

        form.setData('countryCode', updatedValue);
        form.setData('stateProvince', null);
    }

    function handleOnChangeProvince(event) {
        const updatedValue = State.getStateByCodeAndCountry(event.target.value, stateSelectedCountryCode);

        setStateSelectedProvinceCode(event.target.value);
        //setStateSelectedCity('')

        form.setData('stateProvince', updatedValue.name);

        //setAddressData('city', null); 
    }

    const selectFields =
        [
            {
                inputId: 'countryCode',
                inputName: 'countryCode',
                inputLabel: 'country code',
                placeholder: 'Select Country',
                selectedValue: stateSelectedCountryCode,
                inputOptions: stateCountries,
                inputErrorMessage: form.errors.countryCode,
                onChangeEvent: handleOnChangeCountry,
            },
            {
                inputId: 'stateProvince',
                inputName: 'stateProvince',
                inputLabel: 'state / province',
                placeholder: 'Select State / Province',
                selectedValue: stateSelectedProvinceCode,
                inputOptions: stateProvinces,
                inputErrorMessage: form.errors.stateProvince,
                onChangeEvent: handleOnChangeProvince,
            }
        ];

    return (
        <form
            className='flex flex-col gap-y-8 w-full'
            onSubmit={handleSubmit}
        >
            {/* <section
                className='font-bold text-primary'
            >
                Update your account's profile information and email address.
            </section> */}
            <section
                className='flex flex-col gap-5'
            >
                {
                    selectFields.map(
                        (item, index) => {
                            return (
                                <AppSelectComponent
                                    key={index}
                                    inputId={item.inputId}
                                    inputName={item.inputName}
                                    inputLabel={item.inputLabel}
                                    placeholder={item.placeholder}
                                    selectedValue={item.selectedValue}
                                    inputOptions={item.inputOptions}
                                    inputErrorMessage={item.inputErrorMessage}
                                    onChangeEvent={item.onChangeEvent}
                                />
                            );
                        }
                    )
                }
                {
                    inputFields.map(
                        (item, index) => {
                            return (
                                <div
                                    key={index}
                                    className='flex flex-col gap-y-2'
                                >
                                    <InputLabel
                                        forInput={item.name}
                                        value={item.label}
                                        className={'select-none capitalize font-semibold text-lg'}
                                    />
                                    <div
                                        className='relative'
                                    >
                                        <TextInput
                                            value={item.value}
                                            type={item.type}
                                            name={item.name}
                                            placeholder={item.placeholder}
                                            className="w-full"
                                            onBlur={item.onBlur}
                                            handleChange={e => item.eventOnChange(e)}
                                        />
                                    </div>
                                    <InputError message={item.errorMessage} />
                                </div>
                            );
                        }
                    )
                }
            </section>
            <section
                className='flex justify-end gap-x-5'
            >
                <PrimaryButton
                    type='button'
                    onClick={handleReset}
                    processing={checkIfFormIsDirty() == false}
                >
                    Reset
                </PrimaryButton>
                <PrimaryButton
                    type='submit'
                    processing={checkIfFormIsDirty() == false}
                >
                    Update
                </PrimaryButton>
            </section>
        </form>
    );
}
