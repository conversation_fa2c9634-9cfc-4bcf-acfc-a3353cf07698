//* PACKAGES 
import React, { useState } from "react";
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* COMPONENTS
import AppToggleSwitchComponent from "@/Components/App/AppToggleSwitchComponent";
import AppPromptEmailVerificationComponent from "@/Components/App/AppPromptEmailVerificationComponent";
import AppPromptPasswordVerificationComponent from "@/Components/App/AppPromptPasswordVerificationComponent";

//* PARTIALS
//...

//* STATE
//...

//* UTILS 
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserAccountSetupTwoFactorAuthToggleEmail(
    {
        //...
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! VARIABLES
    //...

    //! STATES
    const [stateShowPromptPassword, setStateShowPromptPassword] = useState(false);
    const [stateShowPromptEmail, setStateShowPromptEmail] = useState(false);

    //! USE EFFECTS
    //... 

    //! FUNCTIONS   
    function handleSubmitSuccessPasswordVerification(action) {
        switch (action) {
            case 'enable':
                setStateShowPromptEmail(true);
                break;

            case 'disable':
                handleDisable();
                break;
        }
    }

    function handleEnable() {
        router.patch(
            route("user-settings.auth.email.enable"),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toggleLoginOption(true, "Email authentication enabled")
                }
            }
        );
    }

    function handleDisable() {
        router.patch(
            route("user-settings.auth.email.disable"),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toggleLoginOption()
                }
            }
        );
    }

    function toggleLoginOption(shouldEnable = false, message = 'Email authentication disabled') {
        router.post(
            route('user-verification-login-options.toggle'),
            {
                value: 'email',
                shouldEnable: shouldEnable
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success(message);
                },
                onError: () => toast.error("Something went wrong!")
            }
        );
    }

    return (
        <div
            className="flex space-between gap-x-4 items-center"
        >
            <AppPromptPasswordVerificationComponent
                show={stateShowPromptPassword}
                onClose={() => setStateShowPromptPassword(false)}
                maxWidth='md'
                onSubmitSuccess={() => {
                    const action = user.enable_email_otp == false ? 'enable' : 'disable';

                    handleSubmitSuccessPasswordVerification(action);
                }
                }

                onSubmitError={() => { }}
            />
            <AppPromptEmailVerificationComponent
                show={stateShowPromptEmail}
                onClose={() => setStateShowPromptEmail(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleEnable()}
                onSubmitError={() => { }}
            />
            <AppToggleSwitchComponent
                isChecked={user.enable_email_otp == true}
                onChangeEvent={() => setStateShowPromptPassword(true)}
            />
            <div
                className="capitalize"
            >
                email authentication
            </div>
        </div>
    );

};
