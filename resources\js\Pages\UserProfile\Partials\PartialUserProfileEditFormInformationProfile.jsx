//* PACKAGES 
import React, { useState } from 'react';
import { router, usePage, useForm } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import InputLabel from '@/Components/InputLabel';
import InputError from '@/Components/InputError';
import TextInput from "@/Components/TextInput";
import PrimaryButton from '@/Components/PrimaryButton';

//* STATE
//...

//* UTILS
//... 

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function PartialUserProfileEditFormInformationProfile(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    const form = useForm(
        'formUpdateInformationProfile',
        {
            firstName: user.first_name,
            lastName: user.last_name,
        }
    );

    //! STATES
    const [stateInitialValues, setStateInitialValues] = useState(
        {
            firstName: user.first_name,
            lastName: user.last_name,
        }
    );

    //! VARIABLES
    const inputFields =
        [
            {
                label: 'first name',
                type: 'text',
                name: 'firstName',
                placeholder: 'John',
                value: form.data.firstName,
                errorMessage: form.errors.firstName,
                onBlur: (() => form.setData('firstName', form.data.firstName.trim())),
                eventOnChange: (e => form.setData('firstName', e.target.value)),
            },
            {
                label: 'last name',
                type: 'text',
                name: 'lastName',
                placeholder: 'Doe',
                value: form.data.lastName,
                errorMessage: form.errors.lastName,
                onBlur: (() => form.setData('lastName', form.data.lastName.trim())),
                eventOnChange: (e => form.setData('lastName', e.target.value)),
            },
        ];

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    function handleSubmit(e) {
        e.preventDefault();

        form.patch(
            route("user-profile.update.profile-information"),
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success("Profile information updated.");

                    handleSetInitialValues();
                },
            }
        );
    }

    function handleSetInitialValues() {
        setStateInitialValues(
            prevState => (
                {
                    ...prevState,
                    firstName: form.data.firstName,
                    lastName: form.data.lastName,
                }
            )
        );
    }

    function handleReset() {
        form.setData(
            {
                firstName: stateInitialValues.firstName,
                lastName: stateInitialValues.lastName
            }
        );
    }

    function checkIfFormIsDirty() {
        if (
            form.data.firstName == stateInitialValues.firstName
            && form.data.lastName == stateInitialValues.lastName
        ) {
            return false;
        }
        else {
            return true;
        }
    }

    return (
        <form
            className='flex flex-col gap-y-8 w-full'
            onSubmit={handleSubmit}
        >
            {/* <section
                className='font-bold text-primary'
            >
                Update your account's profile information and email address.
            </section> */}
            <section
                className='flex flex-col gap-5'
            >
                {
                    inputFields.map(
                        (item, index) => {
                            return (
                                <div
                                    key={index}
                                    className='flex flex-col gap-y-2'
                                >
                                    <InputLabel
                                        forInput={item.name}
                                        value={item.label}
                                        className={'select-none capitalize font-semibold text-lg'}
                                    />
                                    <div
                                        className='relative'
                                    >
                                        <TextInput
                                            value={item.value}
                                            type={item.type}
                                            name={item.name}
                                            placeholder={item.placeholder}
                                            className="w-full"
                                            onBlur={item.onBlur}
                                            handleChange={e => item.eventOnChange(e)}
                                        />
                                    </div>
                                    <InputError message={item.errorMessage} />
                                </div>
                            );
                        }
                    )
                }
            </section>
            <section
                className='flex justify-end gap-x-5'
            >
                <PrimaryButton
                    type='button'
                    onClick={handleReset}
                    processing={checkIfFormIsDirty() == false}
                >
                    Reset
                </PrimaryButton>
                <PrimaryButton
                    type='submit'
                    processing={checkIfFormIsDirty() == false}
                >
                    Update
                </PrimaryButton>
            </section>
        </form>
    );
}
