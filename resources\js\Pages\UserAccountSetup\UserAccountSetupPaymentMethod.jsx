//* PACKAGES
import React, { useState, useEffect } from 'react';
import { Head, usePage, router } from "@inertiajs/react";
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

//* ICONS
//...

//* COMPONENTS
import AppLogoWithTextComponent from "@/Components/App/AppLogoWithTextComponent";
import PaymentMethodFormCreateComponent from '@/Components/PaymentMethod/PaymentMethodFormCreateComponent';
import UserLayout from "@/Layouts/UserLayout";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserRegistrationFormPaymentMethodSetup(
    {
        publicKey
    }
) {
    //! PACKAGE
    const stripePromise = loadStripe(publicKey);
    const stripeOptions =
    {
        mode: 'setup',
        currency: 'usd',
        //! Fully customizable with appearance API.
        appearance: {/*...*/ },
    };

    //! VARIABLES
    //...

    //! STATES
    //...

    //! USE EFFECTS
    useEffect(
        () => {
            window.history.pushState(null, '', window.location.href);
            window.onpopstate = () => {
                window.history.pushState(null, '', window.location.href);
            };
        },
        []
    );

    //! FUNCTIONS   
    function handleFinishSetup() {
        router.patch(
            route('user-account-setup.payment-method.finish'),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
                onSuccess: () => {
                    //...
                },
                onError: () => toast.error("Something went wrong!")
            }
        );
    }

    return (
        <UserLayout
            postRouteName="user-account-setup.two-factor"
            setup={false}
            verified={true}
            additionalMainElementClassNames="bg-gray-100"
        >
            <Head
                title="Payment Method Setup"
            />
            <div
                className="flex flex-col sm:justify-center items-center mt-40"
            >
                <div
                    className="flex flex-col gap-y-8 py-12 px-6 bg-white shadow-lg overflow-hidden sm:rounded  md:w-[550px] "
                >
                    <AppLogoWithTextComponent />
                    <div
                        className="block text-center text-gray-700 text-2xl font-bold select-none"
                    >

                        Add Payment Method
                    </div>
                    <div>
                        <Elements
                            stripe={stripePromise}
                            options={stripeOptions}
                        >
                            <PaymentMethodFormCreateComponent
                                redirectRouteName={'user-account-setup.payment-method.success'}
                            />
                        </Elements>
                    </div>
                    <div
                        className="flex flex-col items-center justify-end space-y-6"
                    >
                        <button
                            className="underline text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                            onClick={handleFinishSetup}
                        >
                            I'll handle this later
                        </button>
                    </div>
                </div>
            </div>
        </UserLayout>
    );
}