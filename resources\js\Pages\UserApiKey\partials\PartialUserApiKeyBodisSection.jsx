//* PACKAGES 
import React, { useState, useEffect } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from 'react-toastify';

//* ICONS
import { MdEdit, MdClose, MdLink } from 'react-icons/md';
import { FiTrash } from 'react-icons/fi';

//* LAYOUTS
//...

//* COMPONENTS
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';
import PrimaryButton from '@/Components/PrimaryButton';

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
import PartialModalBodisApiKeySetupForm from './PartialModalBodisApiKeySetupForm';
import PartialModalBodisApiKeyEditForm from './PartialModalBodisApiKeyEditForm';


export default function PartialUserApiKeyBodisSection(
    {
        //! PROPS
        apiKey

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES
    const [stateModalShowBodisSetupForm, setStateModalShowBodisSetupForm] = useState(false);
    const [stateModalShowBodisEditForm, setStateModalShowBodisEditForm] = useState(false);
    const [stateModalShowPasswordVerificationPrompt, setStateModalShowPasswordVerificationPrompt] = useState(false);


    //! USE EFFECTS
    //...

    //! FUNCTIONS
    //...

    //! FUNCTIONS
    function handleOnRemove() {
        router.delete(
            route('user-api-key.remove'),
            {
                onSuccess: () => {
                    toast.success('Bodis API key removed.');
                },
                onError: () => {
                    toast.error('Something went wrong!');
                }
            }
        )
    }

    return (
        <div
            className='flex justify-between'
        >
            <AppPromptPasswordVerificationComponent
                show={stateModalShowPasswordVerificationPrompt}
                onClose={() => setStateModalShowPasswordVerificationPrompt(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleOnRemove()}
                onSubmitError={() => { }}
            />

            <PartialModalBodisApiKeySetupForm
                isModalOpen={stateModalShowBodisSetupForm}
                handleModalClose={() => setStateModalShowBodisSetupForm(false)}
            />
            <PartialModalBodisApiKeyEditForm
                apiKey={apiKey}
                isModalOpen={stateModalShowBodisEditForm}
                handleModalClose={() => setStateModalShowBodisEditForm(false)}
            />
            <div
                className='flex flex-col gap-y-4'
            >
                <div
                    className='font-semibold'
                >
                    Bodis
                </div>
                <div
                    className='text-gray-500'
                >
                    Setup your Bodis API Key to view additional domain details. You must have a registered <a className="text-primary" href='https://www.bodis.com/login' target='_blank'> Bodis premium account. </a>
                </div>
            </div>
            <div>
                {
                    user.hasApiKeyBodis == false
                        ?
                        <PrimaryButton
                            onClick={
                                () => {
                                    setStateModalShowBodisSetupForm(true);
                                }
                            }
                        >
                            Setup
                        </PrimaryButton>
                        :
                        <div
                            className='flex self-start gap-x-2'
                        >
                            <PrimaryButton
                                className=''
                                onClick={
                                    () => {
                                        setStateModalShowBodisEditForm(true);
                                    }
                                }
                            >
                                <MdEdit
                                    className='h-5 w-5'
                                />
                            </PrimaryButton>
                            <PrimaryButton
                                className=''
                                onClick={
                                    () => {
                                        setStateModalShowPasswordVerificationPrompt(true);
                                    }
                                }
                            >
                                <FiTrash
                                    className='h-5 w-5'
                                />
                            </PrimaryButton>
                        </div>
                }
            </div>
        </div>
    );
}
