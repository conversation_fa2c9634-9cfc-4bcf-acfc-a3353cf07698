import React, { useState } from "react";
import SearchDomainNow from "./SearchDomainNow";
import { FixedSizeList as List } from 'react-window'
import SearchNoDomainFound from "./SearchNoDomainFound";

import { LuChevronFirst, LuChevronLast, LuChevronLeft, LuChevronRight } from "react-icons/lu";
import PrimaryButton from "@/Components/PrimaryButton";
import { toast } from "react-toastify";
import CartCounterState from "@/State/CartCounterState";
import OfferPopUp from "./components/OfferPopUp";

export default function MarketPlaceResults({ auth, searchResult, fees, marketFilter, setMarketFilter, doMarketSearch, gSearch, cart, setCart, modal, showModal, offer, showOffer, offerPrice }) {
    if (searchResult == undefined) return <SearchDomainNow />;
    else if (searchResult.length == 0) return <SearchNoDomainFound />;

    const { setCartCounter } = CartCounterState();

    const pages = [100, 250, 500, 1000, 2500, 5000];

    const [isLoading, setIsLoading] = useState(false);

    const setPage = async (page) => {
        if (page <= 0) return;

        await setMarketFilter({ ...marketFilter, currPage: page });

        doMarketSearch(gSearch, { 'perPage': marketFilter.perPage, 'page': page })
    }

    const setPerPage = async (perPage) => {
        await setMarketFilter({ ...marketFilter, perPage: perPage, currPage: 1 });

        doMarketSearch(gSearch, { 'perPage': perPage })
    }

    const isDomainInCart = (domain) => {
        return cart.includes(domain.root_domain);
    };

    const addToMarketCart = (item) => {
        // setIsLoading(false);
        // toast.error('Currently unavailable');

        setIsLoading(true);
        const payload = {
            'user_id': auth.user.id,
            'tld_id': fees[item.domain_extension].tld_id,
            'name': item.root_domain,
            'price': item.price,
            'is_fast_transfer': item.fast_transfer ? 1 : 0,
            'vendor': 'afternic'
        };

        axios.post(route("mycart.market"), payload)
            .finally(() => {
                setIsLoading(false);
                toast.success('Added to cart.');
                setCart(prev => [...prev, item.root_domain]);
                setCartCounter(cart.length + 1);
            })
    }

    const getDomainPrice = (item) => {
        const extension = item.domain_extension

        if (!['com', 'net', 'org'].includes(extension)) return 69.420

        return ((item.price != null && item.price > 0.00) ? item.price : item.min_offer)
    };

    const getButton = (b, index) => {
        if (isDomainInCart(b)) return <span className="block px-3.5 py-2.5 bg-gray-700 border border-transparent rounded-md font-semibold text-sm text-white tracking-widest bg-opacity-30">
            Added to cart
        </span>

        if(b.price == 0.00) return <button onClick={() => { showOffer(b.root_domain, (b.min_offer > 0 ? b.min_offer : 100)) }}  className="disabled:opacity-50  py-2 px-[18px] flex items-center bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-900 border border-transparent rounded-md font-bold tracking-widest">
            Make Offer
        </button>

        return <PrimaryButton processing={isLoading} type="button" onClick={() => addToMarketCart(searchResult[index])} className="disabled:opacity-50 px-5 py-2.5 transition-all duration-200 font-medium flex items-center space-x-2">
            <span>Add to Cart</span>
        </PrimaryButton>
    }

    const getFormatDiv = ({ index, style }) => <div key={`${index}`} style={{ ...style, top: style.top + 5, height: style.height - 5 }} className="pt-5 bg-white border border-gray-100 rounded-2xl p-6 flex justify-between items-center hover:border-gray-200 transition-all duration-200 shadow-sm">
        <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-800"> {searchResult[index].root_domain} </h3>
            <div className="flex items-center mt-1.5">
                <p className="text-xl font-semibold"> ${(getDomainPrice(searchResult[index])).toLocaleString('en', { useGrouping: true })} </p>
                {searchResult[index].price <= 0 && searchResult[index].min_offer > 0 && (<span className="ml-3 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-semibold text-xs px-2 py-0.5 rounded-full">
                        Minimum Offer
                    </span>
                )}
                {searchResult[index].price <= 0 && searchResult[index].min_offer == 0 && (<span className="ml-3 bg-gradient-to-r from-amber-100 to-yellow-100 text-yellow-800 font-semibold text-xs px-2 py-0.5 rounded-full">
                        No Minimum Offer
                    </span>
                )}
                {searchResult[index].fast_transfer == 1 && (<span className="ml-3 bg-blue-50 text-primary font-semibold text-xs px-2 py-0.5 rounded-full">
                    Fast Transfer
                </span>
                )}
            </div>
        </div>

        {getButton(searchResult[index], index)}
    </div>

    return (
        <div className="mx-auto container max-w-[900px] flex flex-col">

            <OfferPopUp modal={modal} showModal={showModal} offer={offer} min_offer={offerPrice}/>

            <h2 className="text-lg font-semibold text-gray-800 mb-6"></h2>
            <div className="space-y-4 h-full max-h-[530px] overflow-y-hidden">
                <List
                    className="List"
                    height={500}
                    itemCount={searchResult.length}
                    itemSize={112}
                    overscanCount={0}
                    width={'100%'}
                >
                    {getFormatDiv}
                </List>
            </div>
            <div className="flex justify-end w-full mt-2">
                <div className="flex justify-between items-center text-gray-500">
                    <div className="relative mr-2">
                        <label className="text-sm">Rows per page:</label>
                        <select value={marketFilter.perPage} onChange={(e) => { setPerPage(e.target.value) }} className="text-sm cursor-pointer border-b border-l-0 border-t-0 border-r-0 focus:border-b focus:outline-none !ring-0">
                            {pages.map((a) => {
                                return <option value={a} key={`opt-${a}`}>{a}</option>
                            })}
                        </select>
                    </div>
                    <div className="px-2 text-sm">Page {marketFilter.currPage} of {marketFilter.lastPage}</div>
                    <button disabled={marketFilter.currPage == 1 ? true : false} onClick={() => { setPage(1) }} className="disabled:text-gray-300 disabled:cursor-default disabled:rounded-none disabled:bg-transparent cursor-pointer rounded-full p-2 hover:bg-gray-100"><LuChevronFirst className="h-5 w-5" /></button>
                    <button disabled={marketFilter.currPage == 1 ? true : false} onClick={() => { setPage(marketFilter.currPage - 1) }} className="disabled:text-gray-300 disabled:cursor-default disabled:rounded-none disabled:bg-transparent cursor-pointer rounded-full p-2 hover:bg-gray-100"><LuChevronLeft className="h-5 w-5" /></button>
                    <button disabled={marketFilter.currPage == marketFilter.lastPage ? true : false} onClick={() => { setPage(marketFilter.currPage + 1) }} className="disabled:text-gray-300 disabled:cursor-default disabled:rounded-none disabled:bg-transparent cursor-pointer rounded-full p-2 hover:bg-gray-100"><LuChevronRight className="h-5 w-5" /></button>
                    <button disabled={marketFilter.currPage == marketFilter.lastPage ? true : false} onClick={() => { setPage(marketFilter.lastPage) }} className="disabled:text-gray-300 disabled:cursor-default disabled:rounded-none disabled:bg-transparent cursor-pointer rounded-full p-2 hover:bg-gray-100"><LuChevronLast className="h-5 w-5" /></button>
                </div>
            </div>
        </div>
    );
} 