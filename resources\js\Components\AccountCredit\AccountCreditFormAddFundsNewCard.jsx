//* PACKAGES
import React, { useState, useEffect } from 'react';
import { router, usePage } from "@inertiajs/react";
import { useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement } from '@stripe/react-stripe-js';
import { toast } from "react-toastify";

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import Modal from '@/Components/Modal';
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import TextInput from '@/Components/TextInput';
import SecondaryButton from '@/Components/SecondaryButton';
import PrimaryButton from '@/Components/PrimaryButton';
import _PaymentIntentStatus from '@/Constant/_PaymentIntentStatus';
import LoaderSpinner from '../LoaderSpinner';

//* PARTIALS
//...

//* STATE
//...

//* UTILS
import UtilCalculateStripeFees from '@/Util/UtilCalculateStripeFees';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AccountCreditFormAddFundsNewCard(
    {
        //! VARIABLES
        initialSetup = false,
        minimumDepositAmount = 100,

        //! STATES 
        isModalOpen = false,

        //! EVENTS
        handleModalClose
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;
    const stripe = useStripe();
    const elements = useElements();

    //! VARIABLES
    const minimumAmount = minimumDepositAmount;

    //! STATES
    const [stateIsProcessing, setStateIsProcessing] = useState(false);
    const [stateInputAmount, setStateInputAmount] = useState('');
    const [stateInputErrorAmount, setStateInputErrorAmount] = useState(null);
    const [stateInputErrorCard, setStateInputErrorCard] = useState(null);
    const [stateInputAmountProcessing, setStateInputAmountProcessing] = useState('');
    const [stateInputAmountCredit, setStateInputAmountCredit] = useState('');

    //! USE EFFECTS
    useEffect(
        () => {
            const amount = parseFloat(stateInputAmount);

            if (isNaN(amount)) {
                setStateInputAmountProcessing(0);
                setStateInputAmountCredit('0.00');

                return;
            }

            const processingFee = UtilCalculateStripeFees(amount, 'CA');

            setStateInputAmountProcessing(processingFee);
            setStateInputAmountCredit((amount - processingFee).toFixed(2));
        },
        [
            stateInputAmount
        ]
    );

    //! FUNCTIONS    
    function handleSelfClose() {
        setStateIsProcessing(false);
        setStateInputAmount('');
        setStateInputErrorAmount(null);
        setStateInputErrorCard(null);

        handleModalClose();
    }

    async function handleOnSubmit(e) {
        e.preventDefault();

        setStateIsProcessing(true);

        setStateInputErrorAmount(null);
        setStateInputErrorCard(null);

        if (!stripe || !elements) {
            // Stripe.js hasn't loaded yet
            return;
        }

        if (stateInputAmount == null) {
            setStateInputErrorAmount('Please input an amount');

            setStateIsProcessing(false);

            return;
        }

        if (stateInputAmount < minimumAmount) {
            if (minimumAmount == 1) {
                setStateInputErrorAmount('Please input at least $1');
            }
            else {
                setStateInputErrorAmount(`Please input the minimum amount $${minimumAmount}`);
            }

            setStateIsProcessing(false);

            return;
        }

        const cardNumberElement = elements.getElement(CardNumberElement);
        const cardExpiry = elements.getElement(CardExpiryElement);
        const cardCvc = elements.getElement(CardCvcElement);

        const { error: errorPaymentMethod, paymentMethod } = await stripe.createPaymentMethod(
            {
                type: 'card',
                card: elements.getElement(CardNumberElement)
            }
        );

        if (errorPaymentMethod) {
            console.log(errorPaymentMethod);

            setStateInputErrorCard(errorPaymentMethod.message);

            setStateIsProcessing(false);

            return;
        }

        const response = await axios.post(
            route('payment-method.create-payment-intent'),
            {
                amount: stateInputAmount,
                paymentMethodId: paymentMethod.id
            },
            {
                withCredentials: true,
            }
        );

        const { clientSecret, paymentIntentId } = response.data;

        if (clientSecret == null) {
            setStateIsProcessing(false);

            return;
        }

        const { error: confirmError, paymentIntent } = await stripe.confirmCardPayment(
            clientSecret,
            {
                payment_method: paymentMethod.id,
            }
        );

        if (confirmError) {
            setStateIsProcessing(false);

            console.error('[Error] Payment Confirmation', confirmError);

            setStateInputErrorCard(confirmError);
        }
        else if (paymentIntent.status === _PaymentIntentStatus.REQUIRES_CAPTURE) {
            await new Promise(resolve => setTimeout(resolve, 3000)); // for balance transactions to load re: stripe fees

            const otherFees = { bill_total: stateInputAmount }

            let storeEndpoint = 'account.balance.store-stripe';

            if (initialSetup == true) {
                storeEndpoint = "user-account-setup.account-credit.store.stripe";
            }

            router.post(
                route(storeEndpoint),
                {
                    user_id: user.id,
                    other_fees: otherFees,
                    intent: paymentIntentId
                },
                {
                    onSuccess: () => {
                        toast.success('Funds successfully added.');

                        let postRoute = "account.balance.store.success";

                        if (initialSetup == true) {
                            postRoute = "user-account-setup.account-credit.success";
                        }

                        router.post(
                            route(postRoute),
                            {

                            },
                            {
                                replace: true,
                                preserveState: false,
                            }
                        );
                    },
                    onError: () => {
                        toast.error('Something went wrong!');
                        setStateIsProcessing(false);
                    },
                    onFinish: () => {
                        // setStateIsProcessing(false);
                    }
                }
            );
        } else {
            toast.error('Something went wrong!');
            setStateIsProcessing(false);
        }
    };

    return (
        <Modal
            show={isModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='xl'
        >
            {(stateIsProcessing) && <>
                <div className={`
                                absolute inset-0 z-10  bg-gray-500 bg-opacity-50
                                flex flex-col justify-around
                                items-center
                                px-10 pt-5 pb-5
                                gap-y-5
                            `}>
                    <LoaderSpinner />
                </div>
            </>}
            <form
                className={`
                    flex flex-col justify-around
                    px-10 pt-5 pb-5
                    gap-y-5
                ` + (stateIsProcessing ? 'pointer-events-none' : '')}
                onSubmit={(e) => handleOnSubmit(e)}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex justify-between items-center gap-10 text-primary font-semibold text-lg'
                >
                    <div>
                        Add Funds to Account Credit
                    </div>
                    <IoMdCloseCircle
                        onClick={() => handleSelfClose()}
                        className='text-primary ease-in-out duration-100 hover:text-blue-900 h-8 w-8 cursor-pointer'
                    />
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-4'
                >
                    <div
                        className='flex flex-col gap-1 '
                    >
                        <InputLabel forInput="cardNumber" value="Card Number" />

                        <div
                            className={`
                                border border-gray-300 rounded-md py-3 px-3 duration-200 ease-in hover:border-gray-500
                                ${stateInputErrorCard == null ? '' : 'border-danger'}
                            `}
                        >
                            <CardNumberElement />
                        </div>
                    </div>

                    <div
                        className='flex flex-row gap-2'
                    >
                        <div
                            className='flex flex-col gap-1 w-full'
                        >
                            <InputLabel forInput="cardExpiry" value="Expiry Date" />

                            <div
                                className={`
                                    border border-gray-300 rounded-md py-3 px-3 duration-200 ease-in hover:border-gray-500
                                    ${stateInputErrorCard == null ? '' : 'border-danger'}
                                `}
                            >
                                <CardExpiryElement />
                            </div>
                        </div>

                        <div
                            className='flex flex-col gap-1 w-full'
                        >
                            <InputLabel forInput="cardCVC" value="CVC" />

                            <div
                                className={`
                                    border border-gray-300 rounded-md py-3 px-3 duration-200 ease-in hover:border-gray-500
                                    ${stateInputErrorCard == null ? '' : 'border-danger'}
                                `}
                            >
                                <CardCvcElement />
                            </div>
                        </div>
                    </div>


                    <InputError message={stateInputErrorCard} className="text-center" />

                    <hr />

                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel forInput="amount" value="Amount ($)" />

                        <TextInput
                            type="number"
                            name="amount"
                            value={stateInputAmount}
                            min="0"
                            placeholder="Amount"
                            className={`
                                w-full
                                ${stateInputErrorAmount == null ? '' : 'border-danger'}
                            `}
                            handleChange={(e) => {
                                const value = e.currentTarget.value.replace(/[^0-9.]/g, '');
                                setStateInputAmount(value);
                            }
                            }
                        />

                        <InputError message={stateInputErrorAmount} />
                        {
                            minimumAmount == 1
                                ?
                                null
                                :
                                <div
                                    className='text-xs text-slate-400 italic'
                                >
                                    Minimum user deposit is ${minimumAmount}. Processing fees may vary depending on whether the card used is domestic or international.
                                </div>
                        }
                    </div>
                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="processingFee"
                            value="Estimated Processing Fee"
                        />

                        <TextInput
                            type="number"
                            name="processingFee"
                            value={stateInputAmountProcessing}
                            min="0"
                            placeholder="Processing Fee"
                            className={`
                                w-full
                            `}
                            disabled={true}
                        />
                    </div>
                    <div
                        className='flex flex-col gap-2'
                    >
                        <InputLabel
                            forInput="processingFee"
                            value="Estimated Credit Amount"
                        />

                        <TextInput
                            type="number"
                            name="processingFee"
                            value={stateInputAmountCredit}
                            min="0"
                            placeholder="Total Credit Amount"
                            className={`
                                w-full
                            `}
                            disabled={true}
                        />
                    </div>
                </section>

                {/* SECTION FOOTER */}
                <section
                    className='flex justify-end gap-x-5'
                >
                    <SecondaryButton
                        type='button'
                        processing={stateIsProcessing == true}
                        onClick={handleSelfClose}
                    >
                        Cancel
                    </SecondaryButton>
                    <PrimaryButton
                        processing={stateIsProcessing == true}
                        type='submit'
                    >
                        Confirm
                    </PrimaryButton>
                </section>
            </form>
        </Modal>
    );
}