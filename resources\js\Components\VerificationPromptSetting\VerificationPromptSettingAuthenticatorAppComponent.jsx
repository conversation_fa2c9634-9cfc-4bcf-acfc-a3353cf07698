//* PACKAGES
import React, { useState } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';
import AppPromptAuthenticatorAppVerificationComponent from '@/Components/App/AppPromptAuthenticatorAppVerificationComponent';

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function VerificationPromptSettingAuthenticatorAppComponent(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    const [stateModalShowPromptAuthenticatorAppVerification, setStateModalShowPromptAuthenticatorAppVerification] = useState(false);

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    function handleSubmit() {
        router.patch(
            route('user-verification.prompt.set'),
            {
                value: 'authApp',
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('Verification prompt set.')
                },
                onError: () => {
                    toast.success('Something went wrong!')
                }
            }
        );
    }

    function handleError() {
        toast.error('Something went wrong!')
    }

    return (
        <div
            className='flex justify-between items-center'
        >
            <AppPromptAuthenticatorAppVerificationComponent
                show={stateModalShowPromptAuthenticatorAppVerification}
                onClose={() => setStateModalShowPromptAuthenticatorAppVerification(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleSubmit()}
                onSubmitError={() => handleError()}
            />

            <div
                className='flex flex-col gap-y-4 select-none'
            >
                <div
                    className=''
                >
                    Authenticator App
                </div>
            </div>
            <div>
                <AppToggleSwitchComponent
                    isChecked={user.verification_prompt == 'authApp'}
                    isDisabled={user.verification_prompt == 'authApp'}
                    onChangeEvent={
                        () => {
                            setStateModalShowPromptAuthenticatorAppVerification(true);
                        }
                    }
                />
            </div>
        </div>
    );
}
