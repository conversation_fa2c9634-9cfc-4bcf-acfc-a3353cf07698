//* PACKAGES
import React, { useEffect, useState } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";
import { isEmpty } from 'lodash';
import { useForm } from 'laravel-precognition-react-inertia';

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import Modal from '@/Components/Modal';
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import TextInput from '@/Components/TextInput';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PaymentMethodFormSetDefaultComponent(
    {
        //! VARIABLES
        selectedPaymentMethodId,
        selectedPaymentMethodName = null,

        //! STATES 
        isModalOpen = false,

        //! EVENTS
        handleModalClose
    }
) {
    //! PACKAGE
    const form = useForm(
        'post',
        route("payment-method.set-default"),
        {
            cardName: '',
            paymentMethodId: '',
        }
    );

    //! VARIABLES
    //...

    //! STATES
    const [isLoading, setIsLoading] = useState(false);


    //! USE EFFECTS
    useEffect(
        () => {
            form.setData('paymentMethodId', selectedPaymentMethodId)
        },
        [
            selectedPaymentMethodId
        ]

    )

    //! FUNCTIONS   
    function handleSelfClose() {
        //* CLOSE MODAL
        form.reset();
        form.clearErrors();

        handleModalClose();
    }

    function handleOnSubmit(e) {
        e.preventDefault();

        form.clearErrors();

        form.submit(
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('Payment method set as default.')

                    handleSelfClose();
                },
                onError: () => {
                    toast.error('Something went wrong!')
                }
            }
        )
    }

    return (
        <Modal
            show={isModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='xl'
        >
            <form
                className={`
                    flex flex-col justify-around
                    px-10 pt-5 pb-5
                    gap-y-5
                `}
                onSubmit={(e) => handleOnSubmit(e)}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex justify-between items-center gap-10 text-primary font-semibold text-lg'
                >
                    <div>
                        Set Payment Method as Default
                    </div>
                    <IoMdCloseCircle
                        onClick={() => handleSelfClose()}
                        className='text-primary ease-in-out duration-100 hover:text-blue-900 h-8 w-8 cursor-pointer'
                    />
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-4'
                >
                    <div
                        className='text-sm flex flex-col gap-y-4'
                    >
                        <div>
                            This Payment method will be used for offline and automatic payments such as the following:
                        </div>
                        <ul
                            className='list-disc pl-5'
                        >
                            {
                                ['domain renewals'].map((item, index) => <li key={index} className='capitalize'>{item}</li>)
                            }
                        </ul>
                    </div>
                    <input
                        type="hidden"
                        name=""
                    />
                    <div>
                        <InputLabel
                            forInput="cardName"
                        >
                            Please enter {selectedPaymentMethodName == null ? 'the card nickname' : <span className='font-bold text-danger'> {selectedPaymentMethodName}</span>} to continue.
                        </InputLabel>

                        <TextInput
                            type="text"
                            name="cardName"
                            value={form.data.cardName}
                            placeholder="Card Nickname"
                            className="mt-1 block w-full "
                            isFocused={true}
                            autoComplete={'false'}
                            onBlur={() => form.data.cardName.trim()}
                            handleChange={(e) => form.setData('cardName', e.currentTarget.value)}
                        />

                        <InputError message={form.errors.cardName} className="mt-2" />
                    </div>
                </section>

                {/* SECTION FOOTER */}
                <section
                    className='flex justify-end gap-x-5'
                >
                    <SecondaryButton
                        type='button'
                        processing={form.processing == true}
                        onClick={handleSelfClose}
                    >
                        Cancel
                    </SecondaryButton>
                    <PrimaryButton
                        processing={form.processing == true}
                        type='submit'
                    >
                        Confirm
                    </PrimaryButton>
                </section>

            </form>
        </Modal>
    );
}