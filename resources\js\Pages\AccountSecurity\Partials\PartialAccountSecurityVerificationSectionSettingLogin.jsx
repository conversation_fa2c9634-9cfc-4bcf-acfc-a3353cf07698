//* PACKAGES 
import React from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from 'react-toastify';
import { debounce } from 'lodash';

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';

//* STATE
//...

//* UTILS
import UtilCheckIfHasLoginAuthOption from '@/Util/UtilCheckIfHasLoginAuthOption';

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
/////

export default function PartialAccountSecurityVerificationSectionLoginSetting(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    //... 

    //! VARIABLES     
    const debounceTimer = 100;

    const enabledOptions =
    {
        email: UtilCheckIfHasLoginAuthOption('email'),
        authApp: UtilCheckIfHasLoginAuthOption('authApp')
    }

    const options =
        [
            {
                value: 'authApp',
                label: 'authenticator app',
                isAvailable: user.enable_authenticator_app == true,
                isEnabled: enabledOptions.authApp,
            },
            {
                value: 'email',
                label: 'email authentication',
                isAvailable: user.enable_email_otp == true,
                isEnabled: enabledOptions.email,
            }
        ]


    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    const toggleAll = debounce(
        (shouldEnable) => {
            router.post(
                route('user-verification-login-options.toggle-all'),
                {
                    shouldEnable: shouldEnable
                },
                {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: () => {
                        if (shouldEnable) {
                            toast.success("Options enabled.");
                        }
                        else {
                            toast.success("Options disabled.");
                        }
                    },
                    onError: () => toast.error("Something went wrong!")
                }
            );
        },
        debounceTimer
    );

    const toggleOption = debounce(
        (value, shouldEnable) => {
            router.post(
                route('user-verification-login-options.toggle'),
                {
                    value: value,
                    shouldEnable: shouldEnable
                },
                {
                    preserveState: true,
                    preserveScroll: true,
                    onSuccess: () => {
                        if (shouldEnable) {
                            toast.success("Option enabled.");
                        }
                        else {
                            toast.success("Option disabled.");
                        }
                    },
                    onError: () => toast.error("Something went wrong!")
                }
            );
        },
        debounceTimer
    )

    return (
        <div
            className={`
                ${user.enable_authenticator_app == true || user.enable_email_otp == true ? 'flex flex-col gap-5 justify-between' : 'hidden'} 
            `}
        >
            <div
                className='flex flex-row justify-between gap-y-4'
            >
                <div
                    className='font-semibold'
                >
                    Enabled 2FA Auth on Login
                </div>
                {
                    user.enable_authenticator_app == true && user.enable_email_otp == true
                        ?
                        <AppToggleSwitchComponent
                            isChecked={enabledOptions.email == true && enabledOptions.authApp == true}
                            onChangeEvent={
                                () => {
                                    let shouldEnable = true;

                                    if (enabledOptions.email == true && enabledOptions.authApp == true) {
                                        shouldEnable = false
                                    }

                                    toggleAll(shouldEnable);
                                }
                            }
                        />
                        :
                        null
                }
            </div>
            <div
                className="flex flex-col gap-6 pl-5"
            >
                {
                    options.filter(item => item.isAvailable == true).map(
                        (item, index) => {
                            return (
                                <div
                                    key={index}
                                    className="flex justify-between items-center"
                                >
                                    <div
                                        className='capitalize'
                                    >
                                        {item.label}
                                    </div>
                                    <AppToggleSwitchComponent
                                        isChecked={item.isEnabled}
                                        onChangeEvent={
                                            () => {
                                                toggleOption(item.value, !item.isEnabled);
                                            }
                                        }
                                    />
                                </div>
                            );
                        }
                    )
                }
            </div>
        </div>
    );
}
