//* PACKAGES
import React, { useEffect, useState } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";
import { useStripe, useElements, PaymentElement } from '@stripe/react-stripe-js';

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import Modal from '@/Components/Modal';
import InputError from "@/Components/InputError";
import InputLabel from "@/Components/InputLabel";
import TextInput from '@/Components/TextInput';
import PrimaryButton from '@/Components/PrimaryButton';
import SecondaryButton from '@/Components/SecondaryButton';
import LoaderSpinner from "@/Components/LoaderSpinner";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
import UtilCalculateStripeFees from '@/Util/UtilCalculateStripeFees';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AccountCreditFormAddFundsPaymentMethod(
    {
        //! VARIABLES
        selectedPaymentMethodId,
        selectedPaymentMethodCountry = 'US',
        minimumDepositAmount = 100,
        customMessage = null,
        initialSetup = false,

        //! STATES 
        isModalOpen = false,

        //! EVENTS
        handleModalClose
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    const stripe = useStripe();

    //! VARIABLES
    const minimumAmount = minimumDepositAmount;

    //! STATES
    const [stateIsProcessing, setStateIsProcessing] = useState(false);
    const [stateInputAmount, setStateInputAmount] = useState('');
    const [stateInputAmountProcessing, setStateInputAmountProcessing] = useState('');
    const [stateInputAmountCredit, setStateInputAmountCredit] = useState('');
    const [stateInputErrorAmount, setStateInputErrorAmount] = useState(null);

    //! USE EFFECTS
    useEffect(
        () => {
            const amount = parseFloat(stateInputAmount);

            if (isNaN(amount)) {
                setStateInputAmountProcessing(0);
                setStateInputAmountCredit('0.00');

                return;
            }

            const processingFee = UtilCalculateStripeFees(amount, selectedPaymentMethodCountry);

            setStateInputAmountProcessing(processingFee);
            setStateInputAmountCredit((amount - processingFee).toFixed(2));
        },
        [
            stateInputAmount
        ]
    );

    //! FUNCTIONS   
    function handleSelfClose() {
        //* CLOSE MODAL
        setStateIsProcessing(false);
        setStateInputAmount('');
        setStateInputErrorAmount(null);

        handleModalClose();
    }

    async function handleOnSubmit(e) {
        e.preventDefault();
        setStateIsProcessing(true);
        setStateInputErrorAmount(null);

        if (!stripe) return;

        const showError = (message) => {
            setStateInputErrorAmount(message);
            setStateIsProcessing(false);
        };

        if (stateInputAmount == null) return showError('Please input an amount');

        if (stateInputAmount < minimumAmount) {
            return showError(
                minimumAmount === 1
                    ? 'Please input at least 1$'
                    : 'Please input the minimum amount'
            );
        }

        try {
            const response = await axios.post(
                route('payment-method.create-payment-intent'),
                {
                    amount: stateInputAmount,
                    paymentMethodId: selectedPaymentMethodId,
                },
                {
                    withCredentials: true
                }
            );

            const { clientSecret, paymentIntentId } = response.data;

            if (!clientSecret) return showError('Something Went Wrong!');

            const paymentResult = await stripe.confirmPayment(
                {
                    clientSecret,
                    redirect: 'if_required',
                }
            );

            //? wait for balance to update
            await new Promise((resolve) => setTimeout(resolve, 3000));

            if (paymentResult.error) return showError('Something Went Wrong!');

            const otherFees = { bill_total: stateInputAmount };
            const storeEndpoint = initialSetup
                ? 'user-account-setup.account-credit.store.stripe'
                : 'account.balance.store-stripe';

            router.post(
                route(storeEndpoint),
                {
                    user_id: user.id,
                    other_fees: otherFees,
                    intent: paymentIntentId,
                },
                {
                    onSuccess: () => {
                        toast.success('Funds successfully added.');

                        const postRoute = initialSetup
                            ? 'user-account-setup.account-credit.success'
                            : 'account.balance.store.success';

                        router.post(
                            route(postRoute),
                            {},
                            {
                                replace: true,
                                preserveState: false,
                            }
                        );
                    },
                    onError: () => {
                        toast.error('Something went wrong!');
                        setStateIsProcessing(false);
                    },
                }
            );
        }
        catch (err) {
            showError('Something Went Wrong!');
        }
    }

    return (
        <Modal
            show={isModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='xl'
        >
            {(stateIsProcessing) && <>
                <div className={`
                                absolute inset-0 z-10  bg-gray-500 bg-opacity-50
                                flex flex-col justify-around
                                items-center
                                px-10 pt-5 pb-5
                                gap-y-5
                            `}>
                    <LoaderSpinner />
                </div>
            </>}
            <form
                className={`
                    flex flex-col justify-around
                    px-10 pt-5 pb-5
                    gap-y-5
                `+ (stateIsProcessing ? 'pointer-events-none' : '')}
                onSubmit={(e) => handleOnSubmit(e)}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex justify-between items-center gap-10 text-primary font-semibold text-lg'
                >
                    <div>
                        Add Funds to Account Credit
                    </div>
                    <IoMdCloseCircle
                        onClick={() => handleSelfClose()}
                        className='text-primary ease-in-out duration-100 hover:text-blue-900 h-8 w-8 cursor-pointer'
                    />
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-4'
                >
                    {
                        customMessage != null ?
                            <div
                                className='lock font-medium text-sm text-gray-700'
                            >
                                {customMessage}
                            </div>
                            :
                            null
                    }
                    <div
                        className='flex flex-col gap-4'
                    >
                        <div
                            className='flex flex-col gap-2'
                        >
                            <InputLabel
                                forInput="amount"
                                value="Please enter the amount to be credited ($)"
                            />

                            <TextInput
                                type="number"
                                name="amount"
                                value={stateInputAmount}
                                min="0"
                                placeholder="Amount"
                                className={`
                                    w-full
                                    ${stateInputErrorAmount == null ? '' : 'border-danger'}
                                `}
                                handleChange={(e) => {
                                    const value = e.currentTarget.value.replace(/[^0-9.]/g, '');

                                    setStateInputAmount(value);
                                }}
                            />

                            <InputError message={stateInputErrorAmount} />
                            {
                                minimumAmount == 1
                                    ?
                                    null
                                    :
                                    <div
                                        className='text-xs text-slate-400 italic'
                                    >
                                        Minimum user deposit is ${minimumAmount}. Processing fees may vary depending on whether the card used is domestic or international.
                                    </div>
                            }
                        </div>
                    </div>
                    <div
                        className='flex flex-col gap-4'
                    >
                        <div
                            className='flex flex-col gap-2'
                        >
                            <InputLabel
                                forInput="processingFee"
                                value="Processing Fee"
                            />

                            <TextInput
                                type="number"
                                name="processingFee"
                                value={stateInputAmountProcessing}
                                min="0"
                                placeholder="Processing Fee"
                                className={`
                                    w-full
                                `}
                                disabled={true}
                            />
                        </div>
                    </div>
                    <div
                        className='flex flex-col gap-4'
                    >
                        <div
                            className='flex flex-col gap-2'
                        >
                            <InputLabel
                                forInput="processingFee"
                                value="Total Credit Amount"
                            />

                            <TextInput
                                type="number"
                                name="processingFee"
                                value={stateInputAmountCredit}
                                min="0"
                                placeholder="Total Credit Amount"
                                className={`
                                    w-full
                                `}
                                disabled={true}
                            />
                        </div>
                    </div>
                </section>

                {/* SECTION FOOTER */}
                <section
                    className='flex justify-end gap-x-5'
                >
                    <SecondaryButton
                        type='button'
                        processing={stateIsProcessing == true}
                        onClick={handleSelfClose}
                    >
                        Cancel
                    </SecondaryButton>
                    <PrimaryButton
                        processing={stateIsProcessing == true}
                        type='submit'
                    >
                        Confirm
                    </PrimaryButton>
                </section>

            </form>

        </Modal>
    );
}