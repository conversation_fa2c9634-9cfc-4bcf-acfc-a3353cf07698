//* PACKAGES 
import React, { useState } from "react";
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* COMPONENTS
import AppToggleSwitchComponent from "@/Components/App/AppToggleSwitchComponent";
import AppPromptPasswordVerificationComponent from "@/Components/App/AppPromptPasswordVerificationComponent";
import AuthAppModalFormSetupComponent from "@/Components/AuthApp/AuthAppModalFormSetupComponent";
import AuthAppModalFormRecoveryCodesComponent from "@/Components/AuthApp/AuthAppModalFormRecoveryCodesComponent";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
import UtilGenerateRandomString from "@/Util/UtilGenerateRandomString";

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialUserAccountSetupTwoFactorAuthToggleAuthApp(
    {
        secretKey,
        qrCodeImageUrl
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! VARIABLES
    //...

    //! STATES
    const [stateShowPromptPassword, setStateShowPromptPassword] = useState(false);

    const [stateInputRecoveryCodes, setStateInputRecoveryCodes] = useState(Array.from({ length: 5 }, () => UtilGenerateRandomString(12, 'alphanumeric', true)));
    const [stateShowModalAuthSetup, setStateShowModalAuthSetup] = useState(false);
    const [stateShowModalRecoveryCodes, setStateShowModalRecoveryCodes] = useState(false);

    //! USE EFFECTS
    //... 

    //! FUNCTIONS   
    function handleSubmitSuccessPasswordVerification(action) {
        switch (action) {
            case 'enable':
                setStateShowModalAuthSetup(true);
                break;

            case 'disable':
                handleDisable();
                break;
        }
    }


    function handleDisable() {
        router.patch(
            route('user-settings.authenticator-app.disable'),
            {
                //...
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toggleLoginOption();
                }
            }
        );
    }

    function toggleLoginOption(shouldEnable = false, message = 'Authenticator App Disabled') {
        router.post(
            route('user-verification-login-options.toggle'),
            {
                value: 'authApp',
                shouldEnable: shouldEnable
            },
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    if (shouldEnable != true) {
                        toast.success(message);
                    }
                },
                onError: () => toast.error("Something went wrong!")
            }
        );
    }

    return (
        <div
            className="flex space-between gap-x-4 items-center"
        >
            <AppPromptPasswordVerificationComponent
                show={stateShowPromptPassword}
                onClose={() => setStateShowPromptPassword(false)}
                maxWidth='md'
                onSubmitSuccess={() => {
                    const action = user.enable_authenticator_app == false ? 'enable' : 'disable';

                    handleSubmitSuccessPasswordVerification(action);
                }
                }
                onSubmitError={() => { }}
            />

            <AuthAppModalFormSetupComponent
                secretKey={secretKey}
                qrCodeImageUrl={qrCodeImageUrl}
                recoveryCodes={stateInputRecoveryCodes}
                maxWidth='2xl'
                isModalOpen={stateShowModalAuthSetup}
                handleModalClose={
                    (openModalRecoveryCodes = false) => {
                        setStateShowModalAuthSetup(false)
                        setStateShowModalRecoveryCodes(openModalRecoveryCodes);

                        if (openModalRecoveryCodes == true) {
                            toggleLoginOption(true, 'Authenticator App Enabled')
                        }
                    }
                }
            />

            <AuthAppModalFormRecoveryCodesComponent
                isModalOpen={stateShowModalRecoveryCodes}
                recoveryCodes={stateInputRecoveryCodes}
                handleModalClose={() => setStateShowModalRecoveryCodes(false)}
            />

            <AppToggleSwitchComponent
                isChecked={user.enable_authenticator_app == true}
                onChangeEvent={() => setStateShowPromptPassword(true)}
            />
            <div
                className="capitalize"
            >
                authenticator app
            </div>
        </div>
    );

};
