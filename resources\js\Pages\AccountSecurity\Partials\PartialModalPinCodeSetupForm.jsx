//* PACKAGES
import React, { useRef, useState } from "react";
import { toast } from "react-toastify";
import { useForm } from 'laravel-precognition-react-inertia';

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import Modal from "@/Components/Modal";
import PrimaryButton from '@/Components/PrimaryButton';
import InputError from "@/Components/InputError";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialModalPinCodeSetupForm(
    {
        //! VARIABLES
        //...

        //! STATES 
        isModalOpen,

        //! EVENTS
        handleModalClose,
    }
) {
    //! PACKAGE    
    const form = useForm(
        'patch',
        route("user-pin-code.update"),
        {
            code: '',
        }
    );

    //! STATES
    const [inputCode, setInputCode] = useState('');
    const [isCodeContainerActive, setIsCodeContainerActive] = useState(false);

    const refCodeInput = useRef(null);
    const refInputs = useRef([]);
    const refCodeContainer = useRef(null);

    //! VARIABLES 

    //! FUNCTIONS 
    function eventOnInputOtp(e) {
        const value = e.currentTarget.value;

        if (value.length != 0) {
            if (isNaN(value)) {
                return;
            }
        }

        if (value.length <= 6) {
            for (let i = 0; i < refInputs.current.length; i++) {
                refInputs.current[i].innerText = value[i] ?? '';
            }

            setInputCode(value);
        }
    }

    //! FUNCTIONS
    function handleSelfClose() {
        //* CLOSE MODAL
        form.reset();
        form.clearErrors();

        handleModalClose();
    }

    function handleSubmit(e) {
        e.preventDefault();

        form.clearErrors();

        form.submit(
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('PIN code successfully set.')
                    handleSelfClose();
                },
                onError: () => {
                    toast.success('Something went wrong!')
                }
            }
        );
    }

    return (
        <Modal
            show={isModalOpen}
            onClose={handleSelfClose}
            maxWidth='md'
        >
            <form
                onSubmit={handleSubmit}
                className={`
                    flex flex-col justify-around
                    px-10 pt-5 pb-10
                    gap-y-8
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex flex-col'
                >
                    <div
                        className="flex justify-end"
                    >
                        <button
                            type="button"
                            className="text-primary ease-in-out duration-100 hover:text-blue-900"
                            onClick={handleSelfClose}
                        >
                            <IoMdCloseCircle
                                className="h-8 w-8 "
                            />
                        </button>
                    </div>
                    <div
                        className="flex flex-col gap-y-5 select-none"
                    >
                        <div
                            className="text-center"
                        >
                            <span
                                className="p-3 bg-primary rounded-sm text-white font-bold text-3xl"
                            >
                                SD
                            </span>
                        </div>
                        <div
                            className="block text-center text-gray-700 text-xl"
                        >
                            Setup your PIN code
                        </div>
                    </div>
                </section>

                {/* SECTION BODY */}
                <section
                    className='flex flex-col gap-4'
                >
                    <div
                        className="flex flex-col gap-y-2"
                    >
                        <div
                            className="flex flex-col gap-8"
                        >
                            <div
                                ref={refCodeContainer}
                                className="flex gap-2"
                                onClick={
                                    () => {
                                        setIsCodeContainerActive(true);

                                        for (let i = 0; i < refInputs.current.length; i++) {
                                            refInputs.current[i].classList.remove('border-gray-400');
                                            refInputs.current[i].classList.add('border-blue-400');
                                        }

                                        refCodeInput.current.focus()
                                    }
                                }
                            >
                                {
                                    [...Array(6).fill(1)].map(
                                        (item, index) => {
                                            return (
                                                <div
                                                    key={index}
                                                    ref={(e) => refInputs.current[index] = e}
                                                    className="flex justify-center items-center w-14 h-14 rounded-lg cursor-pointer ease-in duration-200 border-gray-400 border text-primary text-2xl content-center"
                                                />
                                            )
                                        }
                                    )
                                }
                            </div>
                            <InputError message={form.errors.code} className="text-center" />
                        </div>

                        <input
                            ref={refCodeInput}
                            className="absolute w-0 h-0 p-0 b-0 focus:border-none focus:ring-0 border-0"
                            type="text"
                            id="codeInput"
                            name="codeInput"
                            autoComplete="off"
                            onChange={
                                (e) => {
                                    const value = e.target.value;
                                    const lastCharacter = value.charAt(value.length - 1);

                                    if (isNaN(lastCharacter)) {
                                        refCodeInput.current.value = value.slice(0, -1);
                                    }

                                    if (value.length > 6) {
                                        refCodeInput.current.value = value.slice(0, 6);
                                    }

                                    form.setData('code', refCodeInput.current.value);

                                    eventOnInputOtp(e)
                                }
                            }
                        />
                    </div>
                </section>

                {/* SECTION FOOTER */}
                <section
                    className='flex justify-end gap-x-2'
                >
                    {/* <PrimaryButton
                        type='button'
                        onClick={handleSelfClose}
                    >
                        Close
                    </PrimaryButton> */}
                    <PrimaryButton
                        type='submit'
                        className="w-full"
                        processing={form.data.code.length < 6}
                    >
                        Submit
                    </PrimaryButton>
                </section>
            </form>
        </Modal>
    );
}
