//* PACKAGES 
import React, { Fragment, useRef, useState, useEffect } from "react";
import { Dialog, Transition } from '@headlessui/react';
import { useForm } from 'laravel-precognition-react-inertia';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import InputError from "@/Components/InputError";
import PrimaryButton from "@/Components/PrimaryButton";

//* PARTIALS
//...

//* STATE
//...

//* UTILS 
import { evaluate } from "@/Util/AxiosResponseHandler";

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...


export default function ProfileEmailUpdateCodeVerificationModal(
    {
        //! VARIABLES
        children,
        className,
        email,
        show = false,
        maxWidth = 'lg',
        closeable = true,
        overflow = 'overflow-hidden',

        //! STATES
        //...

        //! EVENTS
        onSubmitSuccess = () => { },
        onSubmitError = () => { },
        onClose = () => { }
    }
) {
    //! VARIABLES 
    const form = useForm(
        'post',
        route("user-verification.email"),
        {
            code: '',
        }
    );

    const [resendButtonDisabled, setResendButtonDisabled] = useState(false);
    const [resendCodeButtonTimerDisabled, setResendCodeButtonTimerDisabled] = useState(0);
    const [isCodeContainerActive, setIsCodeContainerActive] = useState(false);
    const [inputCode, setInputCode] = useState('');

    const refCodeInput = useRef(null);
    const refInputs = useRef([]);
    const refCodeContainer = useRef(null);

    const maxWidthClass =
        {
            sm: 'sm:max-w-sm',
            md: 'sm:max-w-md',
            lg: 'sm:max-w-lg',
            xl: 'sm:max-w-xl',
            '2xl': 'sm:max-w-2xl',
            '3xl': 'sm:max-w-3xl',
            '4xl': 'sm:max-w-4xl',
            '5xl': 'sm:max-w-5xl',
            '6xl': 'sm:max-w-6xl'
        }[maxWidth];

    function eventOnInputOtp(e) {
        const value = e.currentTarget.value;

        if (value.length != 0) {
            if (isNaN(value)) {
                return;
            }
        }

        if (value.length <= 6) {
            for (let i = 0; i < refInputs.current.length; i++) {
                refInputs.current[i].innerText = value[i] ?? '';
            }

            setInputCode(value);
        }
    }

    //! FUNCTIONS
    function handleSelfClose() {
        //* CLOSE MODAL
        form.reset();
        form.clearErrors();

        onClose();
    }

    function startResendButtonDisabledTimer() {
        setResendButtonDisabled(true);
        setResendCodeButtonTimerDisabled(120);

        const countdown = setInterval(
            () => {
                setResendCodeButtonTimerDisabled(
                    (prev) => {
                        if (prev <= 1) {
                            clearInterval(countdown);

                            setResendButtonDisabled(false);

                            return 0;
                        }

                        return prev - 1;
                    }
                );
            },
            1000
        );
    }

    async function handleOnSubmit(e) {
        e.preventDefault();

        form.clearErrors();

        let response = await axios
            .post(
                route("user-profile.update.email"),
                form.data
            )
            .then(
                (response) => {
                    return response;
                }
            )
            .catch(
                (error) => {
                    return error.response;
                }
            );

        response = evaluate(response);

        if (response.success) {
            handleSelfClose()
            toast.success('Email successfully updated.');
            onSubmitSuccess();
        }
        else {
            onSubmitError();
            form.setError(response.errors);
        }
    }

    async function eventOnClickResendCode(e) {
        form.clearErrors();

        e.preventDefault();

        let response = await axios
            .post(
                route("user-profile.update.email.verify"),
                {
                    email: email
                }
            )
            .then(
                (response) => {
                    return response;
                }
            )
            .catch(
                (error) => {
                    return error.response;
                }
            );

        response = evaluate(response);

        if (response.success) {
            toast.info("Code sent. Check your email.");

            //! Disable the button and start the countdown
            startResendButtonDisabledTimer();
        }
        else {
            //console.log(response); 

            if (response.errors.rateLimitMessage) {
                startResendButtonDisabledTimer();

                toast.error(response.errors.rateLimitMessage, { autoClose: 5000 });

                form.setError('code', 'Too Many Attempts. Please Wait 2 Minutes Before Resending the Email.');
            }
            else {
                toast.error('Something went wrong!');
            }
        }
    }

    return (
        <Transition
            show={show}
            as={Fragment}
            leave="duration-200"
        >
            <Dialog
                as="div"
                id="modal"
                className="fixed inset-0 flex overflow-y-auto px-4 py-6 sm:px-0 items-center z-50 transform transition-all w-full"
                onClose={close}
            >
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="absolute inset-0 bg-gray-500/75" />
                </Transition.Child>

                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    enterTo="opacity-100 translate-y-0 sm:scale-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                    leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                    <Dialog.Panel
                        className={`mb-6 bg-white rounded-lg shadow-xl transform transition-all sm:w-full sm:mx-auto ${maxWidthClass} ${overflow} ${className}`}
                    >
                        <form
                            onSubmit={handleOnSubmit}
                            className="flex flex-col justify-around gap-y-12 pt-5 pb-10 px-10"
                        >
                            <div
                                className="flex flex-col"
                            >
                                <div
                                    className="flex justify-end"
                                >
                                    <button
                                        type="button"
                                        className="text-primary ease-in-out duration-100 hover:text-blue-900"
                                        onClick={handleSelfClose}
                                    >
                                        <IoMdCloseCircle
                                            className="h-8 w-8 "
                                        />
                                    </button>
                                </div>
                                <div
                                    className="flex flex-col gap-y-5 select-none"
                                >
                                    <div
                                        className="text-center"
                                    >
                                        <span
                                            className="p-3 bg-primary rounded-sm text-white font-bold text-3xl"
                                        >
                                            SD
                                        </span>
                                    </div>
                                    <div
                                        className="block text-center text-gray-700 text-xl"
                                    >
                                        Verify Your Email to Continue
                                    </div>
                                    <div
                                        className="grow text-center"
                                    >
                                        Enter the 6-digit code received from your new email
                                    </div>
                                </div>
                            </div>
                            <div
                                className="flex flex-col gap-y-12 items-center grow"
                            >
                                <div
                                    className="flex flex-col gap-y-4"
                                >
                                    <div
                                        className="flex flex-col gap-4"
                                    >
                                        <div
                                            ref={refCodeContainer}
                                            className="flex gap-2"
                                            onClick={
                                                () => {
                                                    setIsCodeContainerActive(true);

                                                    for (let i = 0; i < refInputs.current.length; i++) {
                                                        refInputs.current[i].classList.remove('border-gray-400');
                                                        refInputs.current[i].classList.add('border-blue-400');
                                                    }

                                                    refCodeInput.current.focus()
                                                }
                                            }
                                        >
                                            {
                                                [...Array(6).fill(1)].map(
                                                    (item, index) => {
                                                        return (
                                                            <div
                                                                key={index}
                                                                ref={(e) => refInputs.current[index] = e}
                                                                className="flex justify-center items-center w-14 h-14 rounded-lg cursor-pointer ease-in duration-200 border-gray-400 border text-primary text-2xl content-center"
                                                            />
                                                        )
                                                    }
                                                )
                                            }
                                        </div>
                                        <button
                                            type='button'
                                            className={`${resendButtonDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-primary ease-in hover:text-blue-500'}`}
                                            onClick={(e) => eventOnClickResendCode(e)}
                                            disabled={resendButtonDisabled}
                                        >
                                            Resend Code {resendButtonDisabled && <span> in {resendCodeButtonTimerDisabled}s</span>}
                                        </button>
                                        <InputError message={form.errors.code} className="text-center" />
                                    </div>

                                    <input
                                        ref={refCodeInput}
                                        className="absolute w-0 h-0 p-0 b-0 focus:border-none focus:ring-0 border-0"
                                        type="text"
                                        id="codeInput"
                                        name="codeInput"
                                        autoComplete="off"
                                        onChange={
                                            (e) => {
                                                const value = e.target.value;
                                                const lastCharacter = value.charAt(value.length - 1);

                                                if (isNaN(lastCharacter)) {
                                                    refCodeInput.current.value = value.slice(0, -1);
                                                }

                                                if (value.length > 6) {
                                                    refCodeInput.current.value = value.slice(0, 6);
                                                }

                                                form.setData('code', refCodeInput.current.value);

                                                eventOnInputOtp(e)
                                            }
                                        }
                                    />

                                </div>
                                <PrimaryButton
                                    type="submit"
                                    className="w-full"
                                    processing={inputCode.length < 6}
                                >
                                    Verify
                                </PrimaryButton>
                            </div>
                        </form>
                    </Dialog.Panel>
                </Transition.Child>
            </Dialog>
        </Transition>
    );
}
