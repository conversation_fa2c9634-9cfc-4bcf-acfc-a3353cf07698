import PrimaryButton from '@/Components/PrimaryButton';
import React, { useState } from 'react'

export default function MarketResultItem({ searchResult, cartList, fees }) {

    const [localCart, setLocalCart] = useState([]);

    const isDomainInCart = (domain) => {
        return cartList?.includes(domain.root_domain) || localCart.includes(domain.root_domain);
    };

    const addToMarketCart = (item) => {
        const payload = {
            'user_id': auth.user.id,
            'tld_id': fees[item.domain_extension].tld_id,
            'name': item.root_domain,
            'price': item.price,
            'is_fast_transfer': item.fast_transfer ? 1 : 0,
            'vendor': 'afternic'
        };

        axios.post(route("mycart.market"), payload)
            .finally(() => {
                toast.success('Success.')
                setLocalCart(prev => [...prev, item.domain_name]);
            })
    }

    const getDomainPrice = (item) => {
        const extension = item.domain_extension

        if (!['com', 'net', 'org'].includes(extension)) return 69.420

        return ((item.price != null && item.price > 0.00) ? item.price : fees[extension].price)
    };

    return (
        <div className="space-y-4 h-auto max-h-[530px] overflow-y-auto">
            {searchResult.map((domain, i) => (
                <div
                    key={i}
                    className="bg-white border border-gray-100 rounded-2xl p-6 flex justify-between items-center hover:border-gray-200 transition-all duration-200 shadow-sm"
                >
                    <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-800">
                            {domain.root_domain}
                        </h3>
                        <div className="flex items-center mt-1.5">
                            <p className="text-xl font-semibold">
                                ${(getDomainPrice(domain)).toLocaleString('en', { useGrouping: true })}
                            </p>
                            {domain.fast_transfer == 1 && (
                                <span className="ml-3 bg-blue-50 text-primary font-semibold text-xs px-2 py-0.5 rounded-full">
                                    Fast Transfer
                                </span>
                            )}
                        </div>
                    </div>

                    {isDomainInCart(domain) ? (
                        <span className="px-4 py-2 bg-green-100 text-green-800 rounded-lg text-sm font-medium">
                            Added to cart
                        </span>
                    ) : (
                        <PrimaryButton
                            type="button"
                            onClick={() => addToMarketCart(domain)}
                            className="px-5 py-2.5 transition-all duration-200 font-medium flex items-center space-x-2"
                        >
                            <span>Add to Cart</span>
                        </PrimaryButton>
                    )}
                </div>
            ))}
        </div>
    )
}
