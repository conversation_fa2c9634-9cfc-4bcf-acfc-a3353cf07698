//* PACKAGES
import React, { useState } from 'react';
import { router, usePage } from "@inertiajs/react";
import { toast } from "react-toastify";

//* ICONS
//...

//* LAYOUTS
//...

//* COMPONENTS
import AppToggleSwitchComponent from '@/Components/App/AppToggleSwitchComponent';
import AppPromptPasswordVerificationComponent from '@/Components/App/AppPromptPasswordVerificationComponent';

//* STATE
//...

//* UTILS
//...

//* CONSTANT
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function VerificationPromptSettingPasswordComponent(
    {
        //! VARIABLES
        //...

        //! STATES 
        //...

        //! EVENTS
        //..,
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! STATES 
    const [stateModalShowPasswordVerificationPrompt, setStateModalShowPasswordVerificationPrompt] = useState(false);

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    function handleSubmit() {
        router.patch(
            route('user-verification.prompt.set'),
            {
                value: 'password',
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('Verification prompt set.')
                },
                onError: () => {
                    toast.success('Something went wrong!')
                }
            }
        );
    }

    function handleError() {
        toast.success('Something went wrong!')
    }

    return (
        <div
            className='flex justify-between items-center'
        >
            <AppPromptPasswordVerificationComponent
                show={stateModalShowPasswordVerificationPrompt}
                onClose={() => setStateModalShowPasswordVerificationPrompt(false)}
                maxWidth='md'
                onSubmitSuccess={() => handleSubmit()}
                onSubmitError={() => handleError()}
            />

            <div
                className='flex flex-col gap-y-4 select-none'
            >
                <div
                    className=''
                >
                    Password
                </div>
            </div>
            <div>
                <AppToggleSwitchComponent
                    isChecked={user.verification_prompt == 'password'}
                    isDisabled={user.verification_prompt == 'password'}
                    onChangeEvent={
                        () => {
                            setStateModalShowPasswordVerificationPrompt(true);
                        }
                    }
                />
            </div>
        </div>
    );
}
